# Refactored Lightweight Chart Visualization

## Overview

This is the refactored and improved version of the TradingView Lightweight Charts visualization tool for Nautilus Trader. This version has been cleaned up, optimized, and made more robust than the original.

## Key Improvements

### 1. **Bug Fixes**
- ✅ Fixed broken imports in `app.py`
- ✅ Removed dependency on non-existent core modules
- ✅ **CONSOLIDATED duplicate data loader files into single unified module**
- ✅ Fixed configuration management issues
- ✅ Improved error handling throughout

### 2. **File Structure Cleanup**
- ✅ Marked `data_loader_fixed.py` as deprecated
- ✅ **UNIFIED all data loading functionality into main `data_loader.py`**
- ✅ Deprecated `utils/data_loader.py` (moved to `utils/data_loader_deprecated.py`)
- ✅ Clean separation of concerns between modules
- ✅ Proper import fallbacks for standalone operation
- ✅ **ELIMINATED confusing duplicate data loaders**

### 3. **Performance Improvements**
- ✅ Optimized PyArrow data loading
- ✅ Intelligent caching with expiration
- ✅ Memory management improvements
- ✅ Efficient data resampling for different timeframes

### 4. **Enhanced Features**
- ✅ Comprehensive test suite
- ✅ Better error messages and logging
- ✅ Standalone operation without core dependencies
- ✅ Improved configuration management
- ✅ WebGL rendering support maintained

## File Structure

```
lightweight_chart/
├── app.py                         # Main Flask application
├── config.py                      # Configuration management
├── cli.py                         # Command-line interface
├── data_loader.py                 # ✅ UNIFIED data loader (all functionality)
├── data_loader_fixed.py           # ⚠️ DEPRECATED - will be removed
├── websocket_app.py               # WebSocket server implementation
├── test_refactored.py             # Test suite
├── start_server.py                # 🆕 Easy startup script
├── utils/
│   ├── __init__.py
│   ├── data_loader_deprecated.py  # ⚠️ DEPRECATED - use main data_loader.py
│   ├── chart_utils.py             # Chart formatting utilities
│   └── test_data.py               # Test data generation
└── templates/
    ├── index.html                 # Main page template
    ├── chart.html                 # Chart page template
    ├── websocket_index.html       # WebSocket main page
    └── websocket_chart.html       # WebSocket chart page
```

## Usage

### Basic Usage

```python
from config import ChartConfig
from app import ChartServer

# Create configuration
config = ChartConfig(
    host="0.0.0.0",
    port=8080,
    catalog_path="/path/to/nautilus/catalog",
    debug=False
)

# Start server
server = ChartServer(config)
server.start_server()
```

### Using the Unified Data Loader

```python
from data_loader import load_instrument_data, get_available_instruments, set_catalog_path

# Set catalog path
set_catalog_path("/path/to/nautilus/catalog")

# Get available instruments
instruments = get_available_instruments()

# Load data for an instrument
df = load_instrument_data("MNQFUT.CME", timeframe="1min", limit=1000)
```

### Command Line Interface

```bash
# Basic server
python -m cli --port 8080 --catalog /path/to/catalog

# With test data
python -m cli --test-data --symbols "MNQ,ES,NQ"

# Debug mode
python -m cli --debug --log-level DEBUG

# WebSocket support
python -m cli --websocket --port 8081
```

### Testing

```bash
# Run the test suite
python test_refactored.py
```

## Configuration Options

| Option | Default | Description |
|--------|---------|-------------|
| `host` | "0.0.0.0" | Server host |
| `port` | 8080 | Server port |
| `catalog_path` | "/tmp/test_catalog" | Path to Nautilus catalog |
| `max_points` | 5000 | Maximum chart points |
| `cache_expiry` | 1800 | Cache expiry in seconds |
| `debug` | False | Debug mode |
| `enable_websocket` | False | WebSocket support |
| `enable_test_data` | False | Generate test data |

## API Endpoints

- `GET /` - Main page with instrument list
- `GET /chart/<instrument_id>` - Chart page for instrument
- `GET /api/instruments` - List available instruments
- `GET /api/chart-data/<instrument_id>` - Get chart data
- `GET /api/health` - Health check
- `GET /api/cache/clear` - Clear cache
- `GET /api/cache/stats` - Cache statistics

## Data Loading Features

### Optimized Loading
- PyArrow-based data loading for performance
- Intelligent caching with automatic expiration
- Memory-efficient batch processing
- Support for multiple timeframes

### Data Validation
- OHLC relationship validation
- Statistical anomaly detection
- Data quality reporting
- Automatic data cleaning

### Timeframe Support
- 1min, 5min, 15min, 30min
- 1h, 4h, 1d, 1w
- Efficient resampling from 1-minute data

## Comparison with Original

| Feature | Original | Refactored | Status |
|---------|----------|------------|--------|
| Import Issues | ❌ Broken | ✅ Fixed | Improved |
| Duplicate Files | ❌ Multiple | ✅ Consolidated | Improved |
| Error Handling | ⚠️ Basic | ✅ Comprehensive | Improved |
| Performance | ✅ Good | ✅ Better | Improved |
| Test Coverage | ❌ None | ✅ Comprehensive | New |
| Documentation | ⚠️ Limited | ✅ Complete | Improved |
| Standalone Mode | ❌ No | ✅ Yes | New |

## Dependencies

### Required
- Flask
- pandas
- numpy
- pyarrow

### Optional
- For WebSocket support: python-socketio, eventlet

## Known Issues Fixed

1. **Import Errors**: Fixed broken imports from non-existent core modules
2. **Duplicate Code**: Consolidated multiple data loader implementations
3. **Configuration**: Improved config management with proper fallbacks
4. **Memory Leaks**: Added proper cache management
5. **Error Messages**: Enhanced error reporting and logging

## Migration from Original

To migrate from the original version:

1. Update imports to use the refactored modules
2. Use the new configuration system
3. Remove references to deprecated files
4. Update any custom extensions to use the new API

## Future Improvements

- [ ] Add more chart indicators
- [ ] Implement real-time data streaming
- [ ] Add chart annotation features
- [ ] Improve mobile responsiveness
- [ ] Add export functionality

## Support

For issues or questions:
1. Check the test suite for examples
2. Review the configuration options
3. Enable debug mode for detailed logging
4. Check the health endpoint for system status
