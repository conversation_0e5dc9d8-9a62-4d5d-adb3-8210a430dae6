#!/usr/bin/env python3
"""
Simple startup script for the refactored lightweight chart server.

This script provides an easy way to start the chart server with sensible defaults
and automatic test data generation if no catalog is available.
"""

import os
import sys
import argparse
import logging
from pathlib import Path

# Add current directory to path for imports
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def setup_logging(debug=False):
    """Set up logging configuration."""
    level = logging.DEBUG if debug else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Reduce noise from third-party libraries
    if not debug:
        logging.getLogger('werkzeug').setLevel(logging.WARNING)

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Start the refactored Nautilus Trader Chart Server",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Start with default settings and test data
  python start_server.py
  
  # Start with real catalog
  python start_server.py --catalog /path/to/nautilus/catalog
  
  # Start on different port
  python start_server.py --port 9090
  
  # Start in debug mode
  python start_server.py --debug
        """
    )
    
    parser.add_argument(
        '--host',
        default='0.0.0.0',
        help='Host to bind to (default: 0.0.0.0)'
    )
    
    parser.add_argument(
        '--port',
        type=int,
        default=8080,
        help='Port to listen on (default: 8080)'
    )
    
    parser.add_argument(
        '--catalog',
        help='Path to Nautilus Trader catalog (if not provided, test data will be generated)'
    )
    
    parser.add_argument(
        '--debug',
        action='store_true',
        help='Enable debug mode'
    )
    
    parser.add_argument(
        '--test-symbols',
        default='MNQFUT.CME,MNQ.CME,TEST',
        help='Symbols for test data (default: MNQFUT.CME,MNQ.CME,TEST)'
    )
    
    parser.add_argument(
        '--test-bars',
        type=int,
        default=2000,
        help='Number of bars for test data (default: 2000)'
    )
    
    args = parser.parse_args()
    
    # Set up logging
    setup_logging(args.debug)
    logger = logging.getLogger(__name__)
    
    try:
        # Import modules
        from config import ChartConfig
        from app import ChartServer
        
        # Determine catalog path
        if args.catalog:
            catalog_path = args.catalog
            enable_test_data = False
            
            # Check if catalog exists
            if not os.path.exists(catalog_path):
                logger.error(f"Catalog path does not exist: {catalog_path}")
                return 1
                
            logger.info(f"Using existing catalog: {catalog_path}")
        else:
            # Use default catalog path and enable test data
            catalog_path = "/home/<USER>/nautilus_trader_fork/catalog"
            enable_test_data = True
            
            # Check if default catalog exists
            if os.path.exists(catalog_path):
                logger.info(f"Using existing catalog: {catalog_path}")
                enable_test_data = False
            else:
                logger.info("No catalog specified, will generate test data")
        
        # Create configuration
        config = ChartConfig(
            host=args.host,
            port=args.port,
            catalog_path=catalog_path,
            debug=args.debug,
            enable_test_data=enable_test_data,
            test_symbols=args.test_symbols,
            test_bars=args.test_bars
        )
        
        # Validate configuration
        if not config.validate():
            logger.error("Configuration validation failed")
            return 1
        
        # Create and start server
        logger.info("=" * 60)
        logger.info("Starting Refactored Nautilus Trader Chart Server")
        logger.info("=" * 60)
        logger.info(f"Server URL: {config.get_server_url()}")
        logger.info(f"Catalog: {config.catalog_path}")
        logger.info(f"Debug mode: {config.debug}")
        logger.info(f"Test data: {config.enable_test_data}")
        
        if config.enable_test_data:
            logger.info(f"Test symbols: {config.test_symbols}")
            logger.info(f"Test bars: {config.test_bars}")
        
        logger.info("=" * 60)
        
        server = ChartServer(config)
        
        # Start the server
        logger.info("Server starting... Press Ctrl+C to stop")
        server.start_server()
        
        return 0
        
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
        return 0
    except ImportError as e:
        logger.error(f"Import error: {e}")
        logger.error("Make sure all required dependencies are installed:")
        logger.error("  pip install flask pandas numpy pyarrow")
        return 1
    except Exception as e:
        logger.error(f"Error starting server: {e}")
        if args.debug:
            import traceback
            logger.debug(traceback.format_exc())
        return 1

if __name__ == "__main__":
    sys.exit(main())
