"""
Command-line interface for the lightweight chart visualization server.

This module provides a comprehensive CLI for launching and managing
the chart visualization server with various configuration options.
"""

import argparse
import logging
import sys
from pathlib import Path
from typing import Optional

# Note: Core dependencies are not available in this refactored version
ConfigManager = None

from .config import ChartConfig, ChartConfigManager

try:
    from .app import ChartServer, run_server
    from .websocket_app import WebSocketChartServer, run_websocket_server
except ImportError:
    ChartServer = None
    run_server = None
    WebSocketChartServer = None
    run_websocket_server = None

logger = logging.getLogger(__name__)


def create_argument_parser() -> argparse.ArgumentParser:
    """Create and configure the argument parser."""
    parser = argparse.ArgumentParser(
        description="Nautilus Trader Chart Server - TradingView Lightweight Charts",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Start basic chart server
  python -m visualization.lightweight_chart.cli --port 8080

  # Start with WebSocket support
  python -m visualization.lightweight_chart.cli --websocket --port 8081

  # Start with test data
  python -m visualization.lightweight_chart.cli --test-data --symbols "TEST,AAPL,MSFT"

  # Start in debug mode
  python -m visualization.lightweight_chart.cli --debug --log-level DEBUG

  # Use custom catalog
  python -m visualization.lightweight_chart.cli --catalog /path/to/catalog
        """
    )

    # Server configuration
    server_group = parser.add_argument_group('Server Configuration')
    server_group.add_argument(
        '--host',
        type=str,
        default='0.0.0.0',
        help='Host to run the server on (default: 0.0.0.0)'
    )
    server_group.add_argument(
        '--port',
        type=int,
        default=8080,
        help='Port to run the server on (default: 8080)'
    )
    server_group.add_argument(
        '--websocket',
        action='store_true',
        help='Enable WebSocket support for real-time updates'
    )
    server_group.add_argument(
        '--websocket-port',
        type=int,
        default=8081,
        help='Port for WebSocket server (default: 8081)'
    )

    # Data configuration
    data_group = parser.add_argument_group('Data Configuration')
    data_group.add_argument(
        '--catalog',
        type=str,
        help='Path to the Nautilus Trader catalog directory'
    )
    data_group.add_argument(
        '--max-points',
        type=int,
        default=5000,
        help='Maximum number of data points to display (default: 5000)'
    )
    data_group.add_argument(
        '--cache-expiry',
        type=int,
        default=1800,
        help='Cache expiry time in seconds (default: 1800)'
    )

    # Test data configuration
    test_group = parser.add_argument_group('Test Data Configuration')
    test_group.add_argument(
        '--test-data',
        action='store_true',
        help='Generate test data if no catalog is specified'
    )
    test_group.add_argument(
        '--symbols',
        type=str,
        default='TEST,AAPL,MSFT',
        help='Comma-separated list of symbols for test data (default: TEST,AAPL,MSFT)'
    )
    test_group.add_argument(
        '--bars',
        type=int,
        default=1000,
        help='Number of bars to generate for test data (default: 1000)'
    )

    # Logging and debugging
    debug_group = parser.add_argument_group('Logging and Debugging')
    debug_group.add_argument(
        '--debug',
        action='store_true',
        help='Run in debug mode'
    )
    debug_group.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='Set the logging level (default: INFO)'
    )

    # Configuration file
    config_group = parser.add_argument_group('Configuration')
    config_group.add_argument(
        '--config',
        type=str,
        help='Path to configuration file'
    )
    config_group.add_argument(
        '--validate-config',
        action='store_true',
        help='Validate configuration and exit'
    )

    # Utility commands
    utility_group = parser.add_argument_group('Utility Commands')
    utility_group.add_argument(
        '--test-connection',
        action='store_true',
        help='Test data source connection and exit'
    )
    utility_group.add_argument(
        '--list-instruments',
        action='store_true',
        help='List available instruments and exit'
    )
    utility_group.add_argument(
        '--health-check',
        action='store_true',
        help='Perform health check and exit'
    )

    return parser


def setup_logging(log_level: str, debug: bool = False):
    """Set up logging configuration."""
    numeric_level = getattr(logging, log_level.upper())

    # Configure root logger
    logging.basicConfig(
        level=numeric_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

    # Adjust third-party loggers
    if not debug:
        logging.getLogger('werkzeug').setLevel(logging.WARNING)
        logging.getLogger('socketio').setLevel(logging.WARNING)
        logging.getLogger('engineio').setLevel(logging.WARNING)


def validate_arguments(args: argparse.Namespace) -> bool:
    """Validate command line arguments."""
    errors = []

    # Validate port ranges
    if not (1024 <= args.port <= 65535):
        errors.append(f"Invalid port number: {args.port}")

    if args.websocket and not (1024 <= args.websocket_port <= 65535):
        errors.append(f"Invalid WebSocket port number: {args.websocket_port}")

    # Validate paths
    if args.catalog and not Path(args.catalog).exists():
        if not args.test_data:
            errors.append(f"Catalog path does not exist: {args.catalog}")

    if args.config and not Path(args.config).exists():
        errors.append(f"Configuration file does not exist: {args.config}")

    # Validate numeric arguments
    if args.max_points <= 0:
        errors.append(f"Invalid max-points value: {args.max_points}")

    if args.cache_expiry <= 0:
        errors.append(f"Invalid cache-expiry value: {args.cache_expiry}")

    if args.bars <= 0:
        errors.append(f"Invalid bars value: {args.bars}")

    if errors:
        for error in errors:
            logger.error(error)
        return False

    return True


def create_config_from_args(args: argparse.Namespace) -> ChartConfig:
    """Create chart configuration from command line arguments."""
    # Start with configuration from file if provided
    if args.config:
        config = ChartConfigManager.load_config(args.config)
    else:
        config = ChartConfig()

    # Override with command line arguments
    if args.host:
        config.host = args.host
    if args.port:
        config.port = args.port
    if args.catalog:
        config.catalog_path = args.catalog
    if args.max_points:
        config.max_points = args.max_points
    if args.cache_expiry:
        config.cache_expiry = args.cache_expiry
    if args.debug:
        config.debug = args.debug
    if args.log_level:
        config.log_level = args.log_level
    if args.websocket:
        config.enable_websocket = args.websocket
        config.websocket_port = args.websocket_port
    if args.test_data:
        config.enable_test_data = args.test_data
        config.test_symbols = args.symbols
        config.test_bars = args.bars

    return config


def test_connection(config: ChartConfig) -> bool:
    """Test connection to data source."""
    try:
        logger.info("Testing data source connection...")

        if config.enable_websocket:
            server = WebSocketChartServer(config)
        else:
            server = ChartServer(config)

        # Test data source validation
        is_valid = server.validate_data_source(config.catalog_path)

        if is_valid:
            logger.info("✓ Data source connection successful")

            # Try to get instruments
            instruments = server.get_available_instruments()
            logger.info(f"✓ Found {len(instruments)} available instruments")

            if instruments:
                logger.info("Sample instruments:")
                for i, instrument in enumerate(instruments[:5]):
                    logger.info(f"  - {instrument['id']}")
                if len(instruments) > 5:
                    logger.info(f"  ... and {len(instruments) - 5} more")

            return True
        else:
            logger.error("✗ Data source connection failed")
            logger.error(f"  Path: {config.catalog_path}")
            logger.error("  Please check the catalog path and ensure it contains valid data")
            return False

    except Exception as e:
        logger.error(f"✗ Connection test failed: {e}")
        return False


def list_instruments(config: ChartConfig) -> bool:
    """List available instruments."""
    try:
        logger.info("Listing available instruments...")

        if config.enable_websocket:
            server = WebSocketChartServer(config)
        else:
            server = ChartServer(config)

        instruments = server.get_available_instruments()

        if instruments:
            logger.info(f"Found {len(instruments)} instruments:")
            for instrument in instruments:
                file_count = instrument.get('file_count', 'unknown')
                logger.info(f"  {instrument['id']} ({file_count} files)")
        else:
            logger.warning("No instruments found")

        return True

    except Exception as e:
        logger.error(f"Error listing instruments: {e}")
        return False


def perform_health_check(config: ChartConfig) -> bool:
    """Perform comprehensive health check."""
    try:
        logger.info("Performing health check...")

        if config.enable_websocket:
            server = WebSocketChartServer(config)
        else:
            server = ChartServer(config)

        health_status = server.health_check()

        logger.info(f"Health Status: {health_status['status']}")

        if 'instruments_available' in health_status:
            logger.info(f"Instruments Available: {health_status['instruments_available']}")

        if 'data_source_valid' in health_status:
            status = "✓" if health_status['data_source_valid'] else "✗"
            logger.info(f"Data Source Valid: {status}")

        if 'server_info' in health_status:
            info = health_status['server_info']
            logger.info(f"Server URL: {info.get('url', 'unknown')}")
            logger.info(f"Catalog Path: {info.get('catalog_path', 'unknown')}")

        if health_status['status'] == 'healthy':
            logger.info("✓ All health checks passed")
            return True
        else:
            logger.warning("⚠ Health check completed with warnings")
            if 'error' in health_status:
                logger.error(f"Error: {health_status['error']}")
            return False

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return False


def main():
    """Main CLI entry point."""
    parser = create_argument_parser()
    args = parser.parse_args()

    # Set up logging
    setup_logging(args.log_level, args.debug)

    try:
        # Validate arguments
        if not validate_arguments(args):
            return 1

        # Create configuration
        config = create_config_from_args(args)

        # Handle utility commands
        if args.validate_config:
            if config.validate():
                logger.info("✓ Configuration is valid")
                return 0
            else:
                logger.error("✗ Configuration validation failed")
                return 1

        if args.test_connection:
            return 0 if test_connection(config) else 1

        if args.list_instruments:
            return 0 if list_instruments(config) else 1

        if args.health_check:
            return 0 if perform_health_check(config) else 1

        # Start the server
        logger.info("Starting Nautilus Trader Chart Server...")
        logger.info(f"Configuration: {config.get_server_url()}")

        if config.enable_websocket:
            logger.info("WebSocket support enabled")
            server = WebSocketChartServer(config)
        else:
            logger.info("Standard HTTP server")
            server = ChartServer(config)

        server.start_server()

        return 0

    except KeyboardInterrupt:
        logger.info("Server stopped by user")
        return 0
    except Exception as e:
        logger.error(f"Error: {e}")
        if args.debug:
            import traceback
            logger.debug(traceback.format_exc())
        return 1


if __name__ == "__main__":
    sys.exit(main())
