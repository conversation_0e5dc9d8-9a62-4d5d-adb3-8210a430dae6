"""
Unified data loading module for TradingView Lightweight Charts implementation.

This module provides both class-based and function-based interfaces for loading
and processing data from Nautilus Trader's parquet files.
"""

from __future__ import annotations

import os
import glob
import time
import logging
import struct
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple, Union
import warnings

try:
    import pandas as pd
    import numpy as np
    import pyarrow as pa
    import pyarrow.parquet as pq
    import pyarrow.dataset as ds
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    pd = None
    np = None
    pa = None
    pq = None
    ds = None

# Note: Core dependencies are not available in this refactored version
# Using standalone implementation instead
ConfigManager = None
DataValidator = None
decode_price_value = None

try:
    from .config import ChartConfig
except ImportError:
    # For standalone testing
    from config import ChartConfig

# Suppress warnings for cleaner logs
if PANDAS_AVAILABLE:
    warnings.filterwarnings('ignore', category=pd.errors.PerformanceWarning)

logger = logging.getLogger(__name__)

# Global variables for function-based interface
CATALOG_PATH = "/tmp/test_catalog"
DATA_CACHE = {}
CACHE_TIMESTAMPS = {}
CACHE_EXPIRY = 1800  # 30 minutes
ARROW_DATASET_CACHE = {}  # Cache for PyArrow datasets
MAX_CACHE_ITEMS = 50  # Maximum number of items to keep in cache

# Constants for Nautilus Trader price encoding/decoding
STANDARD_PRECISION_SCALAR = 1_000_000_000  # 10^9 for standard precision
HIGH_PRECISION_SCALAR = 10_000_000_000_000_000  # 10^16 for high precision

PYARROW_VERSION = tuple(map(int, pa.__version__.split('.'))) if PANDAS_AVAILABLE else (0, 0, 0)


# Utility functions for binary data handling
def format_bytes_to_numeric(value):
    """
    Convert bytes to numeric values if needed.
    Specifically handles Nautilus Trader's binary price format.
    """
    if isinstance(value, bytes):
        try:
            # For 16-byte binary (high precision)
            if len(value) == 16:
                raw_value = int.from_bytes(value, byteorder='little', signed=True)
                return raw_value / HIGH_PRECISION_SCALAR
            # For 8-byte binary (standard precision)
            elif len(value) == 8:
                raw_value = int.from_bytes(value, byteorder='little', signed=True)
                return raw_value / STANDARD_PRECISION_SCALAR
            else:
                # Try to decode as string
                try:
                    return float(value.decode('utf-8'))
                except:
                    logger.warning(f"Could not decode binary value: {value}")
                    return 0.0
        except Exception as e:
            logger.error(f"Error decoding binary price: {e}, value: {value}")
            return 0.0
    return value

def detect_binary_columns(df):
    """Detect columns that contain binary-encoded values."""
    binary_columns = []
    for col in df.columns:
        if df[col].dtype != 'object':
            continue
        sample = df[col].dropna().head(10)
        if len(sample) > 0 and any(isinstance(x, bytes) for x in sample):
            binary_columns.append(col)
    return binary_columns

def convert_binary_columns(df):
    """Convert all binary columns in a DataFrame to numeric values."""
    binary_columns = detect_binary_columns(df)
    if binary_columns:
        logger.info(f"Found binary columns: {binary_columns}")
        for col in binary_columns:
            try:
                df[col] = df[col].apply(format_bytes_to_numeric)
                logger.info(f"Converted binary column: {col}")
            except Exception as e:
                logger.error(f"Error converting binary column {col}: {e}")
    return df

def check_memory_usage():
    """Check memory usage and clear caches if needed."""
    try:
        total_cache_size = len(DATA_CACHE) + len(ARROW_DATASET_CACHE)

        if total_cache_size > MAX_CACHE_ITEMS * 2:
            logger.warning(f"Cache size {total_cache_size} exceeds limit, clearing oldest items")

            # Clear oldest items from data cache
            if len(DATA_CACHE) > MAX_CACHE_ITEMS:
                sorted_keys = sorted(CACHE_TIMESTAMPS.items(), key=lambda x: x[1], reverse=True)
                keep_keys = [k for k, _ in sorted_keys[:20]]
                new_cache = {k: DATA_CACHE[k] for k in keep_keys if k in DATA_CACHE}
                DATA_CACHE.clear()
                DATA_CACHE.update(new_cache)

                new_timestamps = {k: CACHE_TIMESTAMPS[k] for k in keep_keys if k in CACHE_TIMESTAMPS}
                CACHE_TIMESTAMPS.clear()
                CACHE_TIMESTAMPS.update(new_timestamps)

                logger.warning(f"Cleared data cache down to {len(DATA_CACHE)} items")

            return False
        return True
    except Exception as e:
        logger.error(f"Error checking memory usage: {e}")
        return True

if not PANDAS_AVAILABLE:
    # Stub class when pandas is not available
    class ChartDataLoader:
        def __init__(self, config):
            raise ImportError("pandas, numpy, and pyarrow are required for ChartDataLoader. Install with: pip install pandas numpy pyarrow")
else:
    class ChartDataLoader:
        """
        Data loader for chart visualization with caching and optimization.

        This class provides efficient data loading capabilities specifically
        designed for chart visualization, with support for:
        - PyArrow-based optimized loading
        - Intelligent caching with expiration
        - Memory management
        - Data validation and quality checks
        """

        def __init__(self, config: ChartConfig):
            """Initialize the data loader."""
            if not PANDAS_AVAILABLE:
                raise ImportError("pandas, numpy, and pyarrow are required for ChartDataLoader")

            self.config = config
            self.logger = logging.getLogger(self.__class__.__name__)

            # Cache management
            self._data_cache = {}  # Dict[str, DataFrame] when pandas available
            self._cache_timestamps = {}  # Dict[str, float]
            self._arrow_dataset_cache = {}  # Dict[str, Dataset] when pyarrow available

            # Data validation
            self._validator = DataValidator() if DataValidator else None

            # Constants for Nautilus Trader price encoding/decoding
            self.STANDARD_PRECISION_SCALAR = 1_000_000_000  # 10^9
            self.HIGH_PRECISION_SCALAR = 10_000_000_000_000_000  # 10^16

            self.logger.info(f"Initialized ChartDataLoader with catalog: {config.catalog_path}")

        def load_instrument_data(
            self,
            instrument_id: str,
            timeframe: str = '1min',
            start_date: Optional[datetime] = None,
            end_date: Optional[datetime] = None,
            limit: Optional[int] = None,
            before_datetime: Optional[datetime] = None,
            use_cache: bool = True
        ):
            """Load data for an instrument from parquet files."""
            try:
                # Check memory usage and clear caches if needed
                self._check_memory_usage()

                # Create cache key
                cache_key = f"{instrument_id}_{timeframe}_{start_date}_{end_date}_{before_datetime}_{limit}"

                # Check cache first
                if use_cache and self._is_cache_valid(cache_key):
                    self.logger.info(f"Using cached data for {instrument_id}")
                    return self._data_cache[cache_key].copy()

                # Find instrument directory
                instrument_dir = self._find_instrument_directory(instrument_id)
                if not instrument_dir:
                    self.logger.warning(f"Data directory not found for {instrument_id}")
                    return pd.DataFrame()

                self.logger.info(f"Loading data from {instrument_dir} for {instrument_id}")

                # Load data using PyArrow
                df = self._load_with_pyarrow(
                    instrument_dir,
                    instrument_id,
                    start_date=start_date,
                    end_date=end_date,
                    before_datetime=before_datetime,
                    limit=limit
                )

                if df.empty:
                    self.logger.warning(f"No data loaded for {instrument_id}")
                    return pd.DataFrame()

                # Process and validate data
                df = self._process_dataframe(df, instrument_id)

                # Resample if needed
                if timeframe != '1min' and not df.empty:
                    df = self._resample_ohlcv(df, timeframe)

                # Cache the result
                if use_cache and not df.empty:
                    self._cache_data(cache_key, df)

                self.logger.info(f"Successfully loaded {len(df)} rows for {instrument_id}")
                return df

            except Exception as e:
                self.logger.error(f"Error loading data for {instrument_id}: {e}", exc_info=True)
                return pd.DataFrame()

        def load_instrument_data_optimized(
            self,
            instrument_id: str,
            timeframe: str = '1min',
            start_date: Optional[datetime] = None,
            end_date: Optional[datetime] = None,
            limit: Optional[int] = None,
            before_datetime: Optional[datetime] = None,
            use_cache: bool = True
        ):
            """
            Optimized version that reuses 1min data for all timeframes to avoid repeated disk I/O.
            """
            try:
                # Check if we already have this exact request cached
                cache_key = f"{instrument_id}_{timeframe}_{start_date}_{end_date}_{before_datetime}_{limit}"
                
                if use_cache and self._is_cache_valid(cache_key):
                    self.logger.info(f"Using cached resampled data for {instrument_id} {timeframe}")
                    return self._data_cache[cache_key].copy()
                
                # If requesting non-1min timeframe, try to use cached 1min data first
                if timeframe != '1min':
                    base_cache_key = f"{instrument_id}_1min_{start_date}_{end_date}_{before_datetime}_{limit}"
                    
                    if use_cache and self._is_cache_valid(base_cache_key):
                        self.logger.info(f"Using cached 1min data to generate {timeframe} for {instrument_id}")
                        base_df = self._data_cache[base_cache_key].copy()
                        
                        # Resample from cached 1min data
                        resampled_df = self._resample_ohlcv(base_df, timeframe)
                        
                        # Cache the resampled result
                        if use_cache and not resampled_df.empty:
                            self._cache_data(cache_key, resampled_df)
                        
                        return resampled_df
                
                # Fall back to original loading method
                return self.load_instrument_data(
                    instrument_id, timeframe, start_date, end_date, limit, before_datetime, use_cache
                )
                
            except Exception as e:
                self.logger.error(f"Error in optimized load for {instrument_id}: {e}", exc_info=True)
                return pd.DataFrame()

        def get_cache_stats(self):
            """Get cache statistics."""
            return {
                'data_cache_size': len(self._data_cache),
                'arrow_cache_size': len(self._arrow_dataset_cache),
                'cache_expiry': self.config.cache_expiry,
                'max_cache_items': self.config.max_cache_items
            }

        def _check_memory_usage(self):
            """Placeholder for memory usage check."""
            pass

        def _is_cache_valid(self, cache_key: str) -> bool:
            """Check if cached data is still valid."""
            if cache_key not in self._data_cache:
                return False
            
            cache_age = time.time() - self._cache_timestamps.get(cache_key, 0)
            return cache_age < self.config.cache_expiry

        def _find_instrument_directory(self, instrument_id: str):
            """Find the directory containing data for the specified instrument."""
            base_dir = os.path.join(self.config.catalog_path, "data", "bar")

            if not os.path.exists(base_dir):
                return None

            # Look for exact match first
            exact_path = os.path.join(base_dir, instrument_id)
            if os.path.exists(exact_path) and os.path.isdir(exact_path):
                return exact_path

            # Look for partial matches
            for dir_name in os.listdir(base_dir):
                if instrument_id in dir_name and os.path.isdir(os.path.join(base_dir, dir_name)):
                    return os.path.join(base_dir, dir_name)

            return None

        def _load_with_pyarrow(self, instrument_dir, instrument_id, start_date=None, end_date=None, before_datetime=None, limit=None):
            """Load data using PyArrow with optimized performance."""
            try:
                cache_key = f"dataset_{instrument_id}"

                # Check for cached dataset
                if cache_key in self._arrow_dataset_cache:
                    dataset = self._arrow_dataset_cache[cache_key]
                    self.logger.debug(f"Using cached PyArrow dataset for {instrument_id}")
                else:
                    # Create new dataset with partitioning fallbacks
                    dataset = self._create_arrow_dataset(instrument_dir, instrument_id)
                    if dataset is None:
                        return pd.DataFrame()

                    # Cache the dataset
                    self._arrow_dataset_cache[cache_key] = dataset

                # Determine timestamp column
                schema = dataset.schema
                column_names = [field.name for field in schema]
                ts_column = self._find_timestamp_column(column_names)

                if not ts_column:
                    self.logger.error(f"No timestamp column found for {instrument_id}")
                    return pd.DataFrame()

                # Build filter expression
                filter_expression = self._build_filter_expression(
                    ts_column, start_date, end_date, before_datetime
                )

                # Define columns to load
                required_columns = self._get_required_columns(column_names, ts_column)

                # Create scanner with optimized settings
                scanner = dataset.scanner(
                    columns=required_columns,
                    filter=filter_expression,
                    use_threads=True,
                    batch_size=16384  # Optimized batch size
                )

                # Handle PyArrow API version differences
                if hasattr(scanner, 'finish') and callable(scanner.finish):
                    scanner = scanner.finish()
                    self.logger.debug(f"PyArrow version {pa.__version__}: Used ScannerBuilder.finish()")
                else:
                    self.logger.debug(f"PyArrow version {pa.__version__}: Used scanner directly")

                # Load table
                table = scanner.to_table()

                if table.num_rows == 0:
                    self.logger.warning(f"No data found for {instrument_id}")
                    return pd.DataFrame()

                # Apply limit and sorting
                if limit is not None:
                    table = table.sort_by([(ts_column, "descending")])
                    table = table.slice(offset=0, length=limit)
                    table = table.sort_by([(ts_column, "ascending")])
                else:
                    table = table.sort_by([(ts_column, "ascending")])

                # Convert to pandas with optimizations
                df = table.to_pandas(
                    use_threads=True,
                    split_blocks=True,
                    self_destruct=True
                )

                self.logger.info(f"PyArrow loaded {len(df)} rows for {instrument_id}")
                return df

            except Exception as e:
                self.logger.error(f"Error in PyArrow loading for {instrument_id}: {e}")
                return pd.DataFrame()

        def _create_arrow_dataset(self, instrument_dir, instrument_id):
            """Create PyArrow dataset with appropriate partitioning."""
            try:
                # Try different partitioning strategies (hive -> directory -> default)
                for partitioning in ["hive", "directory", None]:
                    try:
                        if partitioning:
                            dataset = ds.dataset(instrument_dir, format="parquet", partitioning=partitioning)
                        else:
                            dataset = ds.dataset(instrument_dir, format="parquet")

                        self.logger.info(f"Created PyArrow dataset with {partitioning or 'default'} partitioning")
                        return dataset

                    except Exception as e:
                        self.logger.debug(f"Failed to create dataset with {partitioning} partitioning: {e}")
                        continue

                self.logger.error(f"Failed to create PyArrow dataset for {instrument_id}")
                return None

            except Exception as e:
                self.logger.error(f"Error creating PyArrow dataset: {e}")
                return None

        def _find_timestamp_column(self, column_names):
            """Find the timestamp column in the dataset."""
            possible_ts_columns = ["ts_event", "timestamp", "time", "date"]
            for col in possible_ts_columns:
                if col in column_names:
                    return col
            return None

        def _build_filter_expression(self, ts_column, start_date, end_date, before_datetime):
            """Build PyArrow filter expression for date filtering."""
            filter_parts = []

            if start_date:
                filter_parts.append(ds.field(ts_column) >= pd.Timestamp(start_date, tz="UTC").value)
            if end_date:
                filter_parts.append(ds.field(ts_column) <= pd.Timestamp(end_date, tz="UTC").value)
            if before_datetime:
                filter_parts.append(ds.field(ts_column) < pd.Timestamp(before_datetime, tz="UTC").value)

            if filter_parts:
                if len(filter_parts) == 1:
                    return filter_parts[0]
                else:
                    # Combine with AND
                    result = filter_parts[0]
                    for part in filter_parts[1:]:
                        result = result & part
                    return result
            return None

        def _get_required_columns(self, column_names, ts_column):
            """Get the list of required columns for loading."""
            required = [ts_column, 'open', 'high', 'low', 'close']
            if 'volume' in column_names:
                required.append('volume')
            
            # Only include columns that actually exist
            return [col for col in required if col in column_names]

        def _process_dataframe(self, df, instrument_id):
            """Process and validate the loaded dataframe."""
            if df.empty:
                return df

            # Standardize timestamp
            df = self._standardize_timestamp(df, instrument_id)
            
            # Standardize OHLCV columns
            df = self._standardize_ohlcv_columns(df, instrument_id)
            
            # Validate OHLC data
            df = self._validate_ohlc_data(df)
            
            return df

        def _standardize_timestamp(self, df, instrument_id):
            """Standardize the timestamp column."""
            # Basic implementation - convert to pandas datetime if needed
            if 'timestamp' in df.columns:
                if not pd.api.types.is_datetime64_any_dtype(df['timestamp']):
                    try:
                        df['timestamp'] = pd.to_datetime(df['timestamp'])
                    except Exception as e:
                        self.logger.warning(f"Could not convert timestamp for {instrument_id}: {e}")
            
            return df

        def _standardize_ohlcv_columns(self, df, instrument_id):
            """Standardize OHLCV columns."""
            required_cols = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
            missing_columns = [col for col in required_cols if col not in df.columns]

            if missing_columns:
                self.logger.warning(f"Missing columns for {instrument_id}: {missing_columns}")
                for col in missing_columns:
                    if col == 'volume':
                        df['volume'] = 1.0
                    elif col in ['open', 'high', 'low', 'close']:
                        self.logger.error(f"Critical OHLC column missing: {col}")
                        return pd.DataFrame()

            # Convert to numeric
            for col in ['open', 'high', 'low', 'close', 'volume']:
                if col in df.columns:
                    try:
                        df[col] = pd.to_numeric(df[col], errors='coerce')
                    except Exception as e:
                        self.logger.error(f"Failed to convert {col} to numeric: {e}")
                        df[col] = np.nan

            return df

        def _validate_ohlc_data(self, df):
            """Validate OHLC data relationships and detect statistical anomalies."""
            if df.empty:
                return df

            try:
                # Statistical validation - detect impossible patterns
                if len(df) > 10:
                    up_bars = (df['close'] > df['open']).sum()
                    down_bars = (df['close'] < df['open']).sum()
                    unchanged = (df['close'] == df['open']).sum()
                    total_bars = len(df)
                    
                    up_ratio = up_bars / total_bars
                    
                    # Check for statistically impossible situations
                    if up_ratio > 0.95:
                        self.logger.warning(
                            f"CRITICAL DATA ISSUE: {up_ratio:.1%} of bars are up bars (close > open). "
                            "This is statistically impossible and suggests data corruption."
                        )
                    elif up_ratio < 0.05:
                        self.logger.warning(
                            f"CRITICAL DATA ISSUE: {up_ratio:.1%} of bars are up bars. "
                            "This is statistically unlikely and suggests data corruption."
                        )
                    
                    # Log distribution for debugging
                    self.logger.info(
                        f"Price movement distribution: Up: {up_bars} ({up_ratio:.1%}), "
                        f"Down: {down_bars} ({down_bars/total_bars:.1%}), "
                        f"Unchanged: {unchanged} ({unchanged/total_bars:.1%})"
                    )

                # Fix invalid high/low relationships
                invalid_high = ((df['high'] < df['open']) | (df['high'] < df['close'])).sum()
                invalid_low = ((df['low'] > df['open']) | (df['low'] > df['close'])).sum()

                if invalid_high > 0:
                    self.logger.warning(f"Fixing {invalid_high} bars with invalid high values")
                    mask = (df['high'] < df['open']) | (df['high'] < df['close'])
                    df.loc[mask, 'high'] = df.loc[mask, ['open', 'close']].max(axis=1)

                if invalid_low > 0:
                    self.logger.warning(f"Fixing {invalid_low} bars with invalid low values")
                    mask = (df['low'] > df['open']) | (df['low'] > df['close'])
                    df.loc[mask, 'low'] = df.loc[mask, ['open', 'close']].min(axis=1)

                return df

            except Exception as e:
                self.logger.error(f"Error validating OHLC data: {e}")
                return df

        def _resample_ohlcv(self, df, timeframe):
            """Resample OHLCV data to different timeframes."""
            if df.empty or timeframe == '1min':
                return df

            try:
                # Set timestamp as index
                df_indexed = df.set_index('timestamp')
                
                # Define resampling rules
                timeframe_map = {
                    '5min': '5T',
                    '15min': '15T',
                    '30min': '30T',
                    '1h': '1H',
                    '4h': '4H',
                    '1d': '1D',
                    '1w': '1W'
                }
                
                freq = timeframe_map.get(timeframe, '1T')
                
                # Resample
                resampled = df_indexed.resample(freq).agg({
                    'open': 'first',
                    'high': 'max',
                    'low': 'min',
                    'close': 'last',
                    'volume': 'sum'
                }).dropna()
                
                # Reset index
                resampled.reset_index(inplace=True)
                
                self.logger.info(f"Resampled from {len(df)} to {len(resampled)} bars for {timeframe}")
                return resampled

            except Exception as e:
                self.logger.error(f"Error resampling to {timeframe}: {e}")
                return df

        def _cache_data(self, cache_key, df):
            """Cache the dataframe."""
            self._data_cache[cache_key] = df.copy()
            self._cache_timestamps[cache_key] = time.time()
            
            # Clean up old cache entries if needed
            if len(self._data_cache) > self.config.max_cache_items:
                # Remove oldest entries
                sorted_items = sorted(self._cache_timestamps.items(), key=lambda x: x[1])
                for key, _ in sorted_items[:len(sorted_items)//2]:
                    if key in self._data_cache:
                        del self._data_cache[key]
                    if key in self._cache_timestamps:
                        del self._cache_timestamps[key]


# Function-based interface for compatibility with existing code
def load_instrument_data(instrument_id, timeframe='1min', start_date=None, end_date=None, limit=None, before_datetime=None, use_cache=True):
    """
    Load instrument data from Nautilus Trader's parquet files.

    This is the main function-based interface that provides compatibility
    with existing code while using the improved implementation.
    """
    try:
        memory_ok = check_memory_usage()

        cache_key = f"{instrument_id}_{timeframe}_{start_date}_{end_date}_{before_datetime}_{limit}"

        if memory_ok and use_cache and cache_key in DATA_CACHE:
            cache_age = time.time() - CACHE_TIMESTAMPS.get(cache_key, 0)
            if cache_age < CACHE_EXPIRY:
                logger.info(f"Using cached data for {instrument_id} (age: {cache_age:.1f}s)")
                return DATA_CACHE[cache_key].copy()
            else:
                logger.info(f"Cache expired for {instrument_id} (age: {cache_age:.1f}s)")

        base_dir = os.path.join(CATALOG_PATH, "data", "bar")
        matching_dirs = []
        if os.path.exists(base_dir):
            for dir_name in os.listdir(base_dir):
                if instrument_id in dir_name and os.path.isdir(os.path.join(base_dir, dir_name)):
                    matching_dirs.append(os.path.join(base_dir, dir_name))

        if not matching_dirs:
            instrument_dir_exact = os.path.join(base_dir, instrument_id)
            if os.path.exists(instrument_dir_exact) and os.path.isdir(instrument_dir_exact):
                matching_dirs = [instrument_dir_exact]
            else:
                logger.warning(f"Data directory not found for {instrument_id} under {base_dir}")
                return pd.DataFrame()

        instrument_dir = matching_dirs[0]
        logger.info(f"Found data directory at {instrument_dir} for {instrument_id}")

        required_columns_proj_pushdown = [
            'ts_event', 'timestamp', 'time', 'date',
            'open', 'high', 'low', 'close',
            'o', 'h', 'l', 'c',
            'price_open', 'price_high', 'price_low', 'price_close',
            'Open', 'High', 'Low', 'Close',
            'volume', 'v', 'vol', 'Volume', 'VOLUME'
        ]
        required_columns_proj_pushdown = sorted(list(set(required_columns_proj_pushdown)))

        logger.info(f"Attempting to load data with PyArrow for {instrument_id} (limit={limit}, before_datetime={before_datetime})")

        df = load_with_pyarrow(
            instrument_dir,
            instrument_id,
            start_date=start_date,
            end_date=end_date,
            columns=required_columns_proj_pushdown,
            before_datetime=before_datetime,
            limit=limit
        )

        if df.empty:
            logger.warning(f"PyArrow loader returned empty DataFrame for {instrument_id}")
            return pd.DataFrame()

        logger.info(f"PyArrow successfully loaded {len(df)} rows for {instrument_id}")

        df = convert_binary_columns(df)

        # Standardize timestamp column
        if "ts_event" in df.columns:
            if "timestamp" not in df.columns or df["timestamp"].isnull().all():
                 df.rename(columns={"ts_event": "timestamp"}, inplace=True)
                 logger.info(f"Renamed 'ts_event' to 'timestamp' for {instrument_id}")

        if "timestamp" not in df.columns or not pd.api.types.is_datetime64_any_dtype(df["timestamp"]):
            found_ts_col = None
            for col_candidate in ["timestamp", "time", "date", "datetime", "ts"]:
                if col_candidate in df.columns and pd.api.types.is_datetime64_any_dtype(df[col_candidate]):
                    found_ts_col = col_candidate
                    break
                elif col_candidate in df.columns and np.issubdtype(df[col_candidate].dtype, np.integer):
                    found_ts_col = col_candidate
                    break

            if found_ts_col and found_ts_col != "timestamp":
                df.rename(columns={found_ts_col: "timestamp"}, inplace=True)
                logger.info(f"Identified and renamed '{found_ts_col}' to 'timestamp' for {instrument_id}")
            elif "timestamp" not in df.columns and isinstance(df.index, pd.DatetimeIndex):
                 df["timestamp"] = df.index
                 df.reset_index(drop=True, inplace=True)
                 logger.info(f"Used DatetimeIndex as 'timestamp' for {instrument_id}")

        if "timestamp" not in df.columns:
            logger.error(f"No valid timestamp column found after PyArrow load for {instrument_id}")
            return pd.DataFrame()

        try:
            if not pd.api.types.is_datetime64_any_dtype(df["timestamp"]):
                if np.issubdtype(df["timestamp"].dtype, np.integer):
                    df["timestamp"] = pd.to_datetime(df["timestamp"], unit="ns", utc=True, errors='coerce')
                else:
                    df["timestamp"] = pd.to_datetime(df["timestamp"], utc=True, errors='coerce')

            if df["timestamp"].dt.tz is None:
                df["timestamp"] = df["timestamp"].dt.tz_localize('UTC')
            else:
                df["timestamp"] = df["timestamp"].dt.tz_convert('UTC')

            df.dropna(subset=['timestamp'], inplace=True)
            if df.empty:
                logger.warning(f"DataFrame became empty after timestamp conversion/cleanup for {instrument_id}")
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"Error processing timestamp column for {instrument_id}: {e}")
            return pd.DataFrame()

        # Standardize OHLCV columns
        ohlc_mapping = {
            'open': 'open', 'high': 'high', 'low': 'low', 'close': 'close',
            'o': 'open', 'h': 'high', 'l': 'low', 'c': 'close',
            'price_open': 'open', 'price_high': 'high', 'price_low': 'low', 'price_close': 'close',
            'Open': 'open', 'High': 'high', 'Low': 'low', 'Close': 'close',
            'opening': 'open', 'closing': 'close'
        }
        volume_mapping = {'v': 'volume', 'vol': 'volume', 'Volume': 'volume', 'VOLUME': 'volume'}

        rename_dict = {}
        for col in df.columns:
            col_lower = str(col).lower()
            if col_lower in ohlc_mapping and ohlc_mapping[col_lower] != col:
                rename_dict[col] = ohlc_mapping[col_lower]
            elif col_lower in volume_mapping and volume_mapping[col_lower] != col:
                rename_dict[col] = volume_mapping[col_lower]

        if rename_dict:
            logger.info(f"Renaming columns for {instrument_id}: {rename_dict}")
            df.rename(columns=rename_dict, inplace=True)

        # Check for potentially swapped open/close columns
        if len(df) > 20 and 'open' in df.columns and 'close' in df.columns:
            up_bars = (df['close'] > df['open']).sum()
            up_ratio = up_bars / len(df)

            if up_ratio > 0.95:
                logger.warning(f"Potential column swap: {up_ratio:.1%} of bars have close > open")

        required_final_cols = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_final_cols if col not in df.columns]

        if missing_columns:
            logger.warning(f"Missing required columns for {instrument_id}: {missing_columns}")
            for col_to_fix in missing_columns[:]:
                if col_to_fix == 'volume' and 'volume' not in df.columns:
                    df['volume'] = 1.0
                    logger.info(f"Created default 'volume' column for {instrument_id}")
                    missing_columns.remove('volume')
            if any(col in missing_columns for col in ['open', 'high', 'low', 'close']):
                 logger.error(f"Critical OHLC columns still missing for {instrument_id}: {missing_columns}")
                 return pd.DataFrame()

        # Ensure numeric types for OHLCV
        for col in ['open', 'high', 'low', 'close', 'volume']:
            if col in df.columns:
                try:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
                except Exception as e:
                    logger.error(f"Failed to convert column {col} to numeric for {instrument_id}: {e}")
                    df[col] = np.nan

        # Validate data
        df = validate_ohlc_data(df)
        df = prepare_dataframe_for_charting(df)

        if timeframe != '1min' and not df.empty:
            df = resample_ohlcv(df, timeframe)
            df = validate_ohlc_data(df)

        if use_cache and not df.empty:
            DATA_CACHE[cache_key] = df.copy()
            CACHE_TIMESTAMPS[cache_key] = time.time()

            if len(DATA_CACHE) > MAX_CACHE_ITEMS or len(ARROW_DATASET_CACHE) > MAX_CACHE_ITEMS:
                logger.warning(f"Cache size exceeded {MAX_CACHE_ITEMS} items, clearing older items")
                check_memory_usage()

        logger.info(f"Successfully processed data for {instrument_id}, timeframe {timeframe}, returning {len(df)} rows.")
        return df

    except Exception as e:
        logger.error(f"Error in load_instrument_data for {instrument_id}: {e}", exc_info=True)
        return pd.DataFrame()


def load_instrument_data_optimized(instrument_id, timeframe='1min', start_date=None, end_date=None, limit=None, before_datetime=None, use_cache=True):
    """
    Optimized version that reuses 1min data for all timeframes to avoid repeated disk I/O.
    """
    # Check if we already have this exact request cached
    cache_key = f"{instrument_id}_{timeframe}_{start_date}_{end_date}_{before_datetime}_{limit}"

    if use_cache and cache_key in DATA_CACHE:
        cache_age = time.time() - CACHE_TIMESTAMPS.get(cache_key, 0)
        if cache_age < CACHE_EXPIRY:
            logger.info(f"Using cached resampled data for {instrument_id} {timeframe}")
            return DATA_CACHE[cache_key].copy()

    # If requesting non-1min timeframe, try to use cached 1min data first
    if timeframe != '1min':
        base_cache_key = f"{instrument_id}_1min_{start_date}_{end_date}_{before_datetime}_{limit}"

        if use_cache and base_cache_key in DATA_CACHE:
            cache_age = time.time() - CACHE_TIMESTAMPS.get(base_cache_key, 0)
            if cache_age < CACHE_EXPIRY:
                logger.info(f"Using cached 1min data to generate {timeframe} for {instrument_id}")
                base_df = DATA_CACHE[base_cache_key].copy()

                # Resample from cached 1min data
                resampled_df = resample_ohlcv(base_df, timeframe)

                # Cache the resampled result
                if use_cache and not resampled_df.empty:
                    DATA_CACHE[cache_key] = resampled_df.copy()
                    CACHE_TIMESTAMPS[cache_key] = time.time()

                return resampled_df

    # Fall back to original loading method
    return load_instrument_data(instrument_id, timeframe, start_date, end_date, limit, before_datetime, use_cache)


def get_available_instruments():
    """Get a list of available instruments from the catalog."""
    try:
        base_dir = os.path.join(CATALOG_PATH, "data", "bar")

        if not os.path.exists(base_dir):
            logger.warning(f"Base directory not found: {base_dir}")
            return []

        instrument_dirs = [d for d in os.listdir(base_dir) if os.path.isdir(os.path.join(base_dir, d))]

        instruments = []
        for instrument_id in instrument_dirs:
            parquet_files = glob.glob(os.path.join(base_dir, instrument_id, "*.parquet"))
            if parquet_files:
                instruments.append({
                    'id': instrument_id,
                    'full_id': instrument_id
                })

        return instruments

    except Exception as e:
        logger.error(f"Error getting available instruments: {e}")
        return []


def set_catalog_path(path):
    """Set the catalog path."""
    global CATALOG_PATH, DATA_CACHE, CACHE_TIMESTAMPS, ARROW_DATASET_CACHE

    if path != CATALOG_PATH:
        DATA_CACHE.clear()
        CACHE_TIMESTAMPS.clear()
        ARROW_DATASET_CACHE.clear()
        logger.info("Catalog path changed, cleared all caches")

    CATALOG_PATH = path
    logger.info(f"Set catalog path to: {CATALOG_PATH}")


def clear_arrow_cache(instrument_id=None):
    """Clear the PyArrow dataset cache."""
    global ARROW_DATASET_CACHE

    if instrument_id:
        keys_to_remove = []
        for key in ARROW_DATASET_CACHE:
            if instrument_id in key:
                keys_to_remove.append(key)

        for key in keys_to_remove:
            del ARROW_DATASET_CACHE[key]
            logger.info(f"Cleared PyArrow dataset for {key} from cache")
    else:
        ARROW_DATASET_CACHE = {}
        logger.info("Cleared all PyArrow datasets from cache")
