#!/usr/bin/env python3
"""
Data loading module for TradingView Lightweight Charts implementation.

This module handles loading and processing data from Nautilus Trader's parquet files.
"""

import os
import pandas as pd
import numpy as np
import glob
import time
import logging
from datetime import datetime
import pyarrow as pa
import pyarrow.parquet as pq
import pyarrow.dataset as ds
import warnings
import struct

# Suppress warnings for cleaner logs
warnings.filterwarnings('ignore', category=pd.errors.PerformanceWarning)

# Configure logging
logger = logging.getLogger(__name__)

# Global variables
CATALOG_PATH = "/tmp/test_catalog"
DATA_CACHE = {}
CACHE_TIMESTAMPS = {}
CACHE_EXPIRY = 1800  # 30 minutes
ARROW_DATASET_CACHE = {}  # Cache for PyArrow datasets
MAX_CACHE_ITEMS = 50  # Maximum number of items to keep in cache # TODO: Re-evaluate this limit

# Constants for Nautilus Trader price encoding/decoding
STANDARD_PRECISION_SCALAR = 1_000_000_000  # 10^9 for standard precision
HIGH_PRECISION_SCALAR = 10_000_000_000_000_000  # 10^16 for high precision

PYARROW_VERSION = tuple(map(int, pa.__version__.split('.')))

def check_memory_usage():
    """
    Check memory usage and clear caches if needed.
    
    Returns:
    --------
    bool
        True if memory usage is good, False if caches were cleared
    """
    try:
        # For now, just check cache sizes and clear if too large
        total_cache_size = len(DATA_CACHE) + len(ARROW_DATASET_CACHE)
        
        if total_cache_size > MAX_CACHE_ITEMS * 2:
            logger.warning(f"Cache size {total_cache_size} exceeds limit, clearing oldest items")
            
            # Clear oldest items from data cache
            if len(DATA_CACHE) > MAX_CACHE_ITEMS:
                # Sort by timestamp and keep only the most recent
                sorted_keys = sorted(CACHE_TIMESTAMPS.items(), key=lambda x: x[1], reverse=True)
                keep_keys = [k for k, _ in sorted_keys[:20]]
                new_cache = {k: DATA_CACHE[k] for k in keep_keys if k in DATA_CACHE}
                DATA_CACHE.clear()
                DATA_CACHE.update(new_cache)
                
                # Update timestamps
                new_timestamps = {k: CACHE_TIMESTAMPS[k] for k in keep_keys if k in CACHE_TIMESTAMPS}
                CACHE_TIMESTAMPS.clear()
                CACHE_TIMESTAMPS.update(new_timestamps)
                
                logger.warning(f"Cleared data cache down to {len(DATA_CACHE)} items")
                
            return False
        return True
    except Exception as e:
        logger.error(f"Error checking memory usage: {e}")
        return True

def format_bytes_to_numeric(value):
    """
    Convert bytes to numeric values if needed.
    Specifically handles Nautilus Trader's binary price format.
    
    Parameters:
    -----------
    value : bytes or numeric
        The value to convert
        
    Returns:
    --------
    numeric
        The converted value
    """
    if isinstance(value, bytes):
        try:
            # For 16-byte binary (high precision)
            if len(value) == 16:
                raw_value = int.from_bytes(value, byteorder='little', signed=True)
                return raw_value / HIGH_PRECISION_SCALAR
            # For 8-byte binary (standard precision)
            elif len(value) == 8:
                raw_value = int.from_bytes(value, byteorder='little', signed=True)
                return raw_value / STANDARD_PRECISION_SCALAR
            else:
                # Try to decode as string
                try:
                    return float(value.decode('utf-8'))
                except:
                    logger.warning(f"Could not decode binary value: {value}")
                    return 0.0
        except Exception as e:
            logger.error(f"Error decoding binary price: {e}, value: {value}")
            # Fallback to 0.0 on error
            return 0.0
    return value

def detect_binary_columns(df):
    """
    Detect columns that contain binary-encoded values.
    
    Parameters:
    -----------
    df : pd.DataFrame
        The DataFrame to check
        
    Returns:
    --------
    list
        A list of column names that contain binary-encoded values
    """
    binary_columns = []
    
    for col in df.columns:
        # Skip non-object columns
        if df[col].dtype != 'object':
            continue
            
        # Check a sample of values
        sample = df[col].dropna().head(10)
        if len(sample) > 0 and any(isinstance(x, bytes) for x in sample):
            binary_columns.append(col)
            
    return binary_columns

def convert_binary_columns(df):
    """
    Convert all binary columns in a DataFrame to numeric values.
    
    Parameters:
    -----------
    df : pd.DataFrame
        The DataFrame containing binary columns
        
    Returns:
    --------
    pd.DataFrame
        The DataFrame with binary columns converted to numeric values
    """
    binary_columns = detect_binary_columns(df)
    
    if binary_columns:
        logger.info(f"Found binary columns: {binary_columns}")
        for col in binary_columns:
            try:
                df[col] = df[col].apply(format_bytes_to_numeric)
                logger.info(f"Converted binary column: {col}")
            except Exception as e:
                logger.error(f"Error converting binary column {col}: {e}")
                
    return df

def prepare_dataframe_for_charting(df):
    """
    Prepare a DataFrame for charting by ensuring required columns and formats.
    
    Parameters:
    -----------
    df : pd.DataFrame
        The DataFrame to prepare
        
    Returns:
    --------
    pd.DataFrame
        The prepared DataFrame
    """
    # Ensure all required columns exist
    required_cols = ['open', 'high', 'low', 'close', 'volume']
    
    for col in required_cols:
        if col not in df.columns:
            # Add empty column if missing
            df[col] = np.nan
    
    # Convert to numeric and check for data issues
    df['open_float'] = pd.to_numeric(df['open'], errors='coerce')
    df['close_float'] = pd.to_numeric(df['close'], errors='coerce')
    
    # Detect potential inversions or data issues
    # First, check if we have a statistically impossible situation where close is ALWAYS > open
    if len(df) > 10:  # Only check if enough data
        always_up = (df['close_float'] > df['open_float']).sum() == len(df)
        always_down = (df['close_float'] < df['open_float']).sum() == len(df)
        
        if always_up:
            logger.warning("CRITICAL DATA ISSUE: All bars have close > open. This is statistically impossible and suggests data corruption.")
        elif always_down:
            logger.warning("CRITICAL DATA ISSUE: All bars have close < open. This is statistically unlikely and suggests data corruption.")
    
    # Calculate price change
    df['price_change'] = df['close_float'] - df['open_float']
    df['is_up'] = df['close_float'] > df['open_float']
    
    return df

def load_with_pyarrow(instrument_dir, instrument_id, start_date=None, end_date=None, columns=None, before_datetime=None, limit=None):
    """
    Load data using PyArrow with optimized performance.
    
    Parameters:
    -----------
    instrument_dir : str
        The directory containing parquet files
    instrument_id : str
        The instrument ID (used for caching)
    start_date : datetime, optional
        The start date for filtering data (inclusive)
    end_date : datetime, optional
        The end date for filtering data (inclusive)
    columns : list, optional
        List of columns to load (projection pushdown)
    before_datetime : datetime, optional
        Filter for data strictly before this timestamp
    limit : int, optional
        Maximum number of rows to return
        
    Returns:
    --------
    pd.DataFrame
        DataFrame containing the loaded data, sorted by timestamp ascending
    """
    try:
        # Check if dataset is already cached
        cache_key = f"{instrument_dir}:{instrument_id}"
        if cache_key in ARROW_DATASET_CACHE:
            dataset = ARROW_DATASET_CACHE[cache_key]
            logger.info(f"Using cached PyArrow dataset for {instrument_id}")
        else:
            # Try different partitioning strategies
            partitioning_strategies = [
                ('hive', ds.HivePartitioning.discover()),
                ('directory', ds.DirectoryPartitioning.discover(['date'])),
                (None, None)
            ]
            
            dataset = None
            for strategy_name, partitioning in partitioning_strategies:
                try:
                    if partitioning:
                        dataset = ds.dataset(instrument_dir, format='parquet', partitioning=partitioning)
                    else:
                        dataset = ds.dataset(instrument_dir, format='parquet')
                    logger.info(f"Created PyArrow dataset using {strategy_name} partitioning for {instrument_id}")
                    break
                except Exception as e:
                    logger.debug(f"Failed to create dataset with {strategy_name} partitioning: {e}")
                    continue
            
            if dataset is None:
                logger.error(f"Could not create PyArrow dataset for {instrument_dir}")
                return pd.DataFrame()
            
            # Cache the dataset
            ARROW_DATASET_CACHE[cache_key] = dataset
        
        # Build filters
        filters = []
        ts_column = 'ts_event'  # Assume timestamp column
        
        if start_date:
            start_timestamp = int(start_date.timestamp() * 1_000_000_000)  # Convert to nanoseconds
            filters.append(ds.field(ts_column) >= start_timestamp)
        
        if end_date:
            end_timestamp = int(end_date.timestamp() * 1_000_000_000)  # Convert to nanoseconds
            filters.append(ds.field(ts_column) <= end_timestamp)
        
        if before_datetime:
            before_timestamp = int(before_datetime.timestamp() * 1_000_000_000)
            filters.append(ds.field(ts_column) < before_timestamp)
        
        # Create scanner with optimizations
        scanner_obj = dataset.scanner(
            columns=columns,
            filter=ds.and_(*filters) if filters else None,
            batch_size=16384  # Default from TPC-H SF1000 settings, adjust as needed
        )
        
        # Handle different PyArrow API versions
        if hasattr(scanner_obj, 'finish') and callable(scanner_obj.finish):
            # This implies scanner_obj is a ScannerBuilder
            scanner = scanner_obj.finish()
            logger.info(f"PyArrow version {pa.__version__}: Used ScannerBuilder.finish() as .finish attribute was found.")
        else:
            # This implies scanner_obj is already a Scanner
            scanner = scanner_obj
            logger.info(f"PyArrow version {pa.__version__}: Used result of dataset.scanner() directly as .finish attribute was NOT found.")
        
        # For full dataset loads without limit or specific time-based filters, use batched reading
        # This is to conserve memory on very large datasets when no specific slice is requested.
        if start_date is None and end_date is None and before_datetime is None and limit is None:
            logger.info(f"Performing batched read for {instrument_id} due to no specific range/limit.")
            batches = []
            record_batch_reader = scanner.to_reader()
            max_batches_for_full_load = 20  # Adjust as needed
            
            for batch in record_batch_reader:
                batches.append(batch)
                if len(batches) > max_batches_for_full_load:
                    batches.pop(0) 
            
            if not batches:
                logger.warning(f"No data found in batched read for {instrument_id}")
                return pd.DataFrame()
            table = pa.Table.from_batches(batches)
        else:
            # For filtered/limited data, load the entire (potentially pre-filtered) table then sort/slice
            table = scanner.to_table()

        if table.num_rows == 0:
            logger.warning(f"PyArrow returned empty table for {instrument_id} with current filters/scan.")
            return pd.DataFrame()

        # Sorting and Limiting using PyArrow table operations for efficiency
        if limit is not None:
            # Sort descending to get the latest records (or latest before a 'before_datetime')
            table = table.sort_by([(ts_column, "descending")])
            # Take the top 'limit'
            table = table.slice(offset=0, length=limit)
            # Sort back to ascending for chronological order
            table = table.sort_by([(ts_column, "ascending")])
            logger.info(f"Applied limit of {limit}, result has {table.num_rows} rows for {instrument_id}.")
        else:
            # If no limit, ensure data is sorted ascending by timestamp
            table = table.sort_by([(ts_column, "ascending")])
        
        df = table.to_pandas(
            use_threads=True,
            split_blocks=True,
            self_destruct=True
        )
        
        # Convert binary columns if any
        df = convert_binary_columns(df)
        
        logger.info(f"PyArrow loaded {len(df)} rows for {instrument_id}")
        return df
    
    except Exception as e:
        logger.error(f"Error in PyArrow loading for {instrument_id}: {e}")
        return pd.DataFrame()

def load_instrument_data(instrument_id, timeframe='1min', start_date=None, end_date=None, limit=None, before_datetime=None, use_cache=True):
    """
    Load instrument data from Nautilus Trader's parquet files.
    
    Parameters:
    -----------
    instrument_id : str
        The instrument ID to load (e.g., 'BTCUSDT-PERP.BINANCE')
    timeframe : str
        The timeframe for resampling ('1min', '5min', '1h', etc.)
    start_date : datetime, optional
        The start date for filtering data (inclusive)
    end_date : datetime, optional
        The end date for filtering data (inclusive) 
    limit : int, optional
        Maximum number of bars to return (most recent)
    before_datetime : datetime, optional
        Filter for data strictly before this timestamp
    use_cache : bool, optional
        Whether to use cached data if available
        
    Returns:
    --------
    pd.DataFrame
        DataFrame with columns: timestamp, open, high, low, close, volume
    """
    try:
        # Generate cache key
        cache_key = f"{instrument_id}:{timeframe}:{start_date}:{end_date}:{limit}:{before_datetime}"
        
        # Check cache first
        if use_cache and cache_key in DATA_CACHE:
            cache_time = CACHE_TIMESTAMPS.get(cache_key, 0)
            if time.time() - cache_time < CACHE_EXPIRY:
                logger.info(f"Using cached data for {instrument_id}")
                return DATA_CACHE[cache_key].copy()
            else:
                # Remove expired cache entry
                del DATA_CACHE[cache_key]
                if cache_key in CACHE_TIMESTAMPS:
                    del CACHE_TIMESTAMPS[cache_key]
        
        # Find instrument directory
        instrument_dirs = glob.glob(os.path.join(CATALOG_PATH, f"*{instrument_id}*"))
        
        if not instrument_dirs:
            logger.warning(f"No data directory found for instrument {instrument_id}")
            return pd.DataFrame()
        
        instrument_dir = instrument_dirs[0]
        logger.info(f"Loading data from: {instrument_dir}")
        
        # Load using PyArrow for better performance
        df = load_with_pyarrow(
            instrument_dir, 
            instrument_id, 
            start_date=start_date, 
            end_date=end_date, 
            before_datetime=before_datetime, 
            limit=limit
        )
        
        if df.empty:
            logger.warning(f"No data found for {instrument_id}")
            return df
        
        # Ensure timestamp column exists and is properly formatted
        timestamp_cols = ['ts_event', 'timestamp', 'ts_init']
        timestamp_col = None
        for col in timestamp_cols:
            if col in df.columns:
                timestamp_col = col
                break
        
        if timestamp_col is None:
            logger.error(f"No timestamp column found in data for {instrument_id}")
            return pd.DataFrame()
        
        # Convert timestamp to datetime
        if df[timestamp_col].dtype == 'int64':
            # Assume nanoseconds timestamp
            df['timestamp'] = pd.to_datetime(df[timestamp_col], unit='ns')
        else:
            df['timestamp'] = pd.to_datetime(df[timestamp_col])
        
        # Ensure we have OHLCV columns
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            logger.warning(f"Missing columns for {instrument_id}: {missing_columns}")
            
            # Try to find alternative column names
            if 'size' in df.columns and 'volume' in missing_columns:
                df['volume'] = df['size']
                logger.info(f"Mapped 'size' to 'volume' for {instrument_id}")
                missing_columns.remove('volume')
            
            # If still missing critical columns, try to create them
            if missing_columns:
                if 'price' in df.columns:
                    # Use price for all OHLC if individual components missing
                    for col in ['open', 'high', 'low', 'close']:
                        if col in missing_columns:
                            df[col] = df['price']
                            logger.info(f"Created '{col}' from 'price' column for {instrument_id}")
                            missing_columns.remove(col)
                
                # Create default volume if missing
                if 'volume' in missing_columns:
                    df['volume'] = 1.0  # Default volume
                    logger.info(f"Created default 'volume' column for {instrument_id}")
                    missing_columns.remove('volume')
                # Add other derivations if necessary, e.g., open from previous close
            if any(col in missing_columns for col in ['open', 'high', 'low', 'close']):
                 logger.error(f"Critical OHLC columns still missing for {instrument_id} after attempting fixes: {missing_columns}")
                 return pd.DataFrame()

        # Ensure numeric types for OHLCV
        for col in ['open', 'high', 'low', 'close', 'volume']:
            if col in df.columns:
                try:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
                except Exception as e:
                    logger.error(f"Failed to convert column {col} to numeric for {instrument_id}: {e}")
                    df[col] = np.nan # Fill with NaN on error
            else: # Should not happen if missing_columns logic is robust
                logger.warning(f"Column {col} expected but not found before numeric conversion for {instrument_id}")


        # Data should already be sorted by timestamp ascending from load_with_pyarrow if limit was used,
        # or if no limit, it's also sorted ascending.
        # A final explicit sort can be a safeguard if needed, but load_with_pyarrow should handle it.
        # df = df.sort_values('timestamp', ascending=True) # This might be redundant

        # The old slicing logic is removed:
        # if len(df) > limit: # 'limit' was 'max_bars'
        #     df = df.sort_values('timestamp', ascending=False).head(limit)
        #     df = df.sort_values('timestamp')
        
        # Validate data to ensure proper OHLC relationships
        df = validate_ohlc_data(df)
        
        df = prepare_dataframe_for_charting(df) # Adds open_float, close_float etc.
        
        if timeframe != '1min' and not df.empty: # Check df is not empty before resampling
            df = resample_ohlcv(df, timeframe)
            # Validate again after resampling
            df = validate_ohlc_data(df)
        
        if use_cache and not df.empty: # Only cache non-empty results
            DATA_CACHE[cache_key] = df.copy() # Store a copy
            CACHE_TIMESTAMPS[cache_key] = time.time()
            
            if len(DATA_CACHE) > MAX_CACHE_ITEMS or len(ARROW_DATASET_CACHE) > MAX_CACHE_ITEMS: # Re-check MAX_CACHE_ITEMS for ARROW_DATASET_CACHE
                logger.warning(f"Cache size exceeded {MAX_CACHE_ITEMS} items, clearing older items")
                check_memory_usage() # This will prune caches
        
        logger.info(f"Successfully processed data for {instrument_id}, timeframe {timeframe}, returning {len(df)} rows.")
        return df
    
    except Exception as e:
        logger.error(f"Error loading data for {instrument_id}: {e}")
        return pd.DataFrame()

def load_instrument_data_optimized(instrument_id, timeframe='1min', start_date=None, end_date=None, limit=None, before_datetime=None, use_cache=True):
    """
    Optimized version of load_instrument_data with enhanced performance.
    
    This is an alias to the main function with potential for future optimizations.
    """
    return load_instrument_data(instrument_id, timeframe, start_date, end_date, limit, before_datetime, use_cache)

def resample_ohlcv(df, timeframe):
    """
    Resample OHLCV data to a different timeframe.
    
    Parameters:
    -----------
    df : pd.DataFrame
        DataFrame with OHLCV data
    timeframe : str
        Target timeframe ('5min', '1h', '4h', '1d', etc.)
        
    Returns:
    --------
    pd.DataFrame
        Resampled DataFrame
    """
    if df.empty:
        return df
    
    try:
        # Set timestamp as index for resampling
        df_indexed = df.set_index('timestamp')
        
        # Convert timeframe to pandas frequency
        freq_map = {
            '1min': '1T',
            '5min': '5T',
            '15min': '15T',
            '30min': '30T',
            '1h': '1H',
            '4h': '4H',
            '1d': '1D'
        }
        
        freq = freq_map.get(timeframe, timeframe)
        
        # Resample OHLCV data
        resampled = df_indexed.resample(freq).agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum'
        }).dropna()
        
        # Reset index to get timestamp back as column
        resampled = resampled.reset_index()
        
        logger.info(f"Resampled data to {timeframe}: {len(df)} -> {len(resampled)} bars")
        return resampled
        
    except Exception as e:
        logger.error(f"Error resampling data to {timeframe}: {e}")
        return df

def validate_ohlc_data(df):
    """
    Validate and clean OHLC data.
    
    Parameters:
    -----------
    df : pd.DataFrame
        DataFrame with OHLC data
        
    Returns:
    --------
    pd.DataFrame
        Validated DataFrame with invalid rows removed
    """
    if df.empty:
        return df
    
    initial_len = len(df)
    
    try:
        # Remove rows where high < low (impossible)
        df = df[df['high'] >= df['low']]
        
        # Remove rows where open/close are outside high/low range
        df = df[
            (df['open'] >= df['low']) & (df['open'] <= df['high']) &
            (df['close'] >= df['low']) & (df['close'] <= df['high'])
        ]
        
        # Remove rows with zero or negative prices
        df = df[
            (df['open'] > 0) & (df['high'] > 0) & 
            (df['low'] > 0) & (df['close'] > 0)
        ]
        
        # Remove rows with NaN values in critical columns
        df = df.dropna(subset=['open', 'high', 'low', 'close'])
        
        removed_count = initial_len - len(df)
        if removed_count > 0:
            logger.warning(f"Removed {removed_count} invalid OHLC rows")
        
        return df
        
    except Exception as e:
        logger.error(f"Error validating OHLC data: {e}")
        return df

def get_available_instruments():
    """
    Get list of available instruments in the catalog.
    
    Returns:
    --------
    list
        List of available instrument IDs
    """
    try:
        instruments = []
        
        if not os.path.exists(CATALOG_PATH):
            logger.warning(f"Catalog path does not exist: {CATALOG_PATH}")
            return instruments
        
        # Look for directories that might contain instrument data
        for item in os.listdir(CATALOG_PATH):
            item_path = os.path.join(CATALOG_PATH, item)
            if os.path.isdir(item_path):
                # Check if directory contains parquet files
                parquet_files = glob.glob(os.path.join(item_path, "*.parquet"))
                if parquet_files:
                    instruments.append(item)
        
        logger.info(f"Found {len(instruments)} instruments in catalog")
        return sorted(instruments)
        
    except Exception as e:
        logger.error(f"Error getting available instruments: {e}")
        return []

def clear_arrow_cache(instrument_id=None):
    """
    Clear PyArrow dataset cache.
    
    Parameters:
    -----------
    instrument_id : str, optional
        Specific instrument to clear from cache. If None, clears all.
    """
    global ARROW_DATASET_CACHE
    
    try:
        if instrument_id:
            # Clear specific instrument
            keys_to_remove = [k for k in ARROW_DATASET_CACHE.keys() if instrument_id in k]
            for key in keys_to_remove:
                del ARROW_DATASET_CACHE[key]
            logger.info(f"Cleared PyArrow cache for {instrument_id}")
        else:
            # Clear all
            ARROW_DATASET_CACHE.clear()
            logger.info("Cleared all PyArrow cache")
            
    except Exception as e:
        logger.error(f"Error clearing PyArrow cache: {e}")

def set_catalog_path(path):
    """
    Set the catalog path for data loading.
    
    Parameters:
    -----------
    path : str
        Path to the data catalog
    """
    global CATALOG_PATH
    CATALOG_PATH = path
    logger.info(f"Set catalog path to: {path}")
    
    # Clear caches when path changes
    DATA_CACHE.clear()
    CACHE_TIMESTAMPS.clear()
    ARROW_DATASET_CACHE.clear()
