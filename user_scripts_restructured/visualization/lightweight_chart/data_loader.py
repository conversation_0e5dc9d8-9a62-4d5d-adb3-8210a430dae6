"""
Fixed data loading module for TradingView Lightweight Charts implementation.
"""

from __future__ import annotations

import os
import glob
import time
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple, Union
import warnings

try:
    import pandas as pd
    import numpy as np
    import pyarrow as pa
    import pyarrow.parquet as pq
    import pyarrow.dataset as ds
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    pd = None
    np = None
    pa = None
    pq = None
    ds = None

# Note: Core dependencies are not available in this refactored version
# Using standalone implementation instead
ConfigManager = None
DataValidator = None
decode_price_value = None

try:
    from .config import ChartConfig
except ImportError:
    # For standalone testing
    from config import ChartConfig

# Suppress warnings for cleaner logs
if PANDAS_AVAILABLE:
    warnings.filterwarnings('ignore', category=pd.errors.PerformanceWarning)

logger = logging.getLogger(__name__)


if not PANDAS_AVAILABLE:
    # Stub class when pandas is not available
    class ChartDataLoader:
        def __init__(self, config):
            raise ImportError("pandas, numpy, and pyarrow are required for ChartDataLoader. Install with: pip install pandas numpy pyarrow")
else:
    class ChartDataLoader:
        """
        Data loader for chart visualization with caching and optimization.

        This class provides efficient data loading capabilities specifically
        designed for chart visualization, with support for:
        - PyArrow-based optimized loading
        - Intelligent caching with expiration
        - Memory management
        - Data validation and quality checks
        """

        def __init__(self, config: ChartConfig):
            """Initialize the data loader."""
            if not PANDAS_AVAILABLE:
                raise ImportError("pandas, numpy, and pyarrow are required for ChartDataLoader")

            self.config = config
            self.logger = logging.getLogger(self.__class__.__name__)

            # Cache management
            self._data_cache = {}  # Dict[str, DataFrame] when pandas available
            self._cache_timestamps = {}  # Dict[str, float]
            self._arrow_dataset_cache = {}  # Dict[str, Dataset] when pyarrow available

            # Data validation
            self._validator = DataValidator() if DataValidator else None

            # Constants for Nautilus Trader price encoding/decoding
            self.STANDARD_PRECISION_SCALAR = 1_000_000_000  # 10^9
            self.HIGH_PRECISION_SCALAR = 10_000_000_000_000_000  # 10^16

            self.logger.info(f"Initialized ChartDataLoader with catalog: {config.catalog_path}")

        def load_instrument_data(
            self,
            instrument_id: str,
            timeframe: str = '1min',
            start_date: Optional[datetime] = None,
            end_date: Optional[datetime] = None,
            limit: Optional[int] = None,
            before_datetime: Optional[datetime] = None,
            use_cache: bool = True
        ):
            """Load data for an instrument from parquet files."""
            try:
                # Check memory usage and clear caches if needed
                self._check_memory_usage()

                # Create cache key
                cache_key = f"{instrument_id}_{timeframe}_{start_date}_{end_date}_{before_datetime}_{limit}"

                # Check cache first
                if use_cache and self._is_cache_valid(cache_key):
                    self.logger.info(f"Using cached data for {instrument_id}")
                    return self._data_cache[cache_key].copy()

                # Find instrument directory
                instrument_dir = self._find_instrument_directory(instrument_id)
                if not instrument_dir:
                    self.logger.warning(f"Data directory not found for {instrument_id}")
                    return pd.DataFrame()

                self.logger.info(f"Loading data from {instrument_dir} for {instrument_id}")

                # Load data using PyArrow
                df = self._load_with_pyarrow(
                    instrument_dir,
                    instrument_id,
                    start_date=start_date,
                    end_date=end_date,
                    before_datetime=before_datetime,
                    limit=limit
                )

                if df.empty:
                    self.logger.warning(f"No data loaded for {instrument_id}")
                    return pd.DataFrame()

                # Process and validate data
                df = self._process_dataframe(df, instrument_id)

                # Resample if needed
                if timeframe != '1min' and not df.empty:
                    df = self._resample_ohlcv(df, timeframe)

                # Cache the result
                if use_cache and not df.empty:
                    self._cache_data(cache_key, df)

                self.logger.info(f"Successfully loaded {len(df)} rows for {instrument_id}")
                return df

            except Exception as e:
                self.logger.error(f"Error loading data for {instrument_id}: {e}", exc_info=True)
                return pd.DataFrame()

        def load_instrument_data_optimized(
            self,
            instrument_id: str,
            timeframe: str = '1min',
            start_date: Optional[datetime] = None,
            end_date: Optional[datetime] = None,
            limit: Optional[int] = None,
            before_datetime: Optional[datetime] = None,
            use_cache: bool = True
        ):
            """
            Optimized version that reuses 1min data for all timeframes to avoid repeated disk I/O.
            """
            try:
                # Check if we already have this exact request cached
                cache_key = f"{instrument_id}_{timeframe}_{start_date}_{end_date}_{before_datetime}_{limit}"
                
                if use_cache and self._is_cache_valid(cache_key):
                    self.logger.info(f"Using cached resampled data for {instrument_id} {timeframe}")
                    return self._data_cache[cache_key].copy()
                
                # If requesting non-1min timeframe, try to use cached 1min data first
                if timeframe != '1min':
                    base_cache_key = f"{instrument_id}_1min_{start_date}_{end_date}_{before_datetime}_{limit}"
                    
                    if use_cache and self._is_cache_valid(base_cache_key):
                        self.logger.info(f"Using cached 1min data to generate {timeframe} for {instrument_id}")
                        base_df = self._data_cache[base_cache_key].copy()
                        
                        # Resample from cached 1min data
                        resampled_df = self._resample_ohlcv(base_df, timeframe)
                        
                        # Cache the resampled result
                        if use_cache and not resampled_df.empty:
                            self._cache_data(cache_key, resampled_df)
                        
                        return resampled_df
                
                # Fall back to original loading method
                return self.load_instrument_data(
                    instrument_id, timeframe, start_date, end_date, limit, before_datetime, use_cache
                )
                
            except Exception as e:
                self.logger.error(f"Error in optimized load for {instrument_id}: {e}", exc_info=True)
                return pd.DataFrame()

        def get_cache_stats(self):
            """Get cache statistics."""
            return {
                'data_cache_size': len(self._data_cache),
                'arrow_cache_size': len(self._arrow_dataset_cache),
                'cache_expiry': self.config.cache_expiry,
                'max_cache_items': self.config.max_cache_items
            }

        def _check_memory_usage(self):
            """Placeholder for memory usage check."""
            pass

        def _is_cache_valid(self, cache_key: str) -> bool:
            """Check if cached data is still valid."""
            if cache_key not in self._data_cache:
                return False
            
            cache_age = time.time() - self._cache_timestamps.get(cache_key, 0)
            return cache_age < self.config.cache_expiry

        def _find_instrument_directory(self, instrument_id: str):
            """Find the directory containing data for the specified instrument."""
            base_dir = os.path.join(self.config.catalog_path, "data", "bar")

            if not os.path.exists(base_dir):
                return None

            # Look for exact match first
            exact_path = os.path.join(base_dir, instrument_id)
            if os.path.exists(exact_path) and os.path.isdir(exact_path):
                return exact_path

            # Look for partial matches
            for dir_name in os.listdir(base_dir):
                if instrument_id in dir_name and os.path.isdir(os.path.join(base_dir, dir_name)):
                    return os.path.join(base_dir, dir_name)

            return None

        def _load_with_pyarrow(self, instrument_dir, instrument_id, start_date=None, end_date=None, before_datetime=None, limit=None):
            """Load data using PyArrow with optimized performance."""
            try:
                cache_key = f"dataset_{instrument_id}"

                # Check for cached dataset
                if cache_key in self._arrow_dataset_cache:
                    dataset = self._arrow_dataset_cache[cache_key]
                    self.logger.debug(f"Using cached PyArrow dataset for {instrument_id}")
                else:
                    # Create new dataset with partitioning fallbacks
                    dataset = self._create_arrow_dataset(instrument_dir, instrument_id)
                    if dataset is None:
                        return pd.DataFrame()

                    # Cache the dataset
                    self._arrow_dataset_cache[cache_key] = dataset

                # Determine timestamp column
                schema = dataset.schema
                column_names = [field.name for field in schema]
                ts_column = self._find_timestamp_column(column_names)

                if not ts_column:
                    self.logger.error(f"No timestamp column found for {instrument_id}")
                    return pd.DataFrame()

                # Build filter expression
                filter_expression = self._build_filter_expression(
                    ts_column, start_date, end_date, before_datetime
                )

                # Define columns to load
                required_columns = self._get_required_columns(column_names, ts_column)

                # Create scanner with optimized settings
                scanner = dataset.scanner(
                    columns=required_columns,
                    filter=filter_expression,
                    use_threads=True,
                    batch_size=16384  # Optimized batch size
                )

                # Handle PyArrow API version differences
                if hasattr(scanner, 'finish') and callable(scanner.finish):
                    scanner = scanner.finish()
                    self.logger.debug(f"PyArrow version {pa.__version__}: Used ScannerBuilder.finish()")
                else:
                    self.logger.debug(f"PyArrow version {pa.__version__}: Used scanner directly")

                # Load table
                table = scanner.to_table()

                if table.num_rows == 0:
                    self.logger.warning(f"No data found for {instrument_id}")
                    return pd.DataFrame()

                # Apply limit and sorting
                if limit is not None:
                    table = table.sort_by([(ts_column, "descending")])
                    table = table.slice(offset=0, length=limit)
                    table = table.sort_by([(ts_column, "ascending")])
                else:
                    table = table.sort_by([(ts_column, "ascending")])

                # Convert to pandas with optimizations
                df = table.to_pandas(
                    use_threads=True,
                    split_blocks=True,
                    self_destruct=True
                )

                self.logger.info(f"PyArrow loaded {len(df)} rows for {instrument_id}")
                return df

            except Exception as e:
                self.logger.error(f"Error in PyArrow loading for {instrument_id}: {e}")
                return pd.DataFrame()

        def _create_arrow_dataset(self, instrument_dir, instrument_id):
            """Create PyArrow dataset with appropriate partitioning."""
            try:
                # Try different partitioning strategies (hive -> directory -> default)
                for partitioning in ["hive", "directory", None]:
                    try:
                        if partitioning:
                            dataset = ds.dataset(instrument_dir, format="parquet", partitioning=partitioning)
                        else:
                            dataset = ds.dataset(instrument_dir, format="parquet")

                        self.logger.info(f"Created PyArrow dataset with {partitioning or 'default'} partitioning")
                        return dataset

                    except Exception as e:
                        self.logger.debug(f"Failed to create dataset with {partitioning} partitioning: {e}")
                        continue

                self.logger.error(f"Failed to create PyArrow dataset for {instrument_id}")
                return None

            except Exception as e:
                self.logger.error(f"Error creating PyArrow dataset: {e}")
                return None

        def _find_timestamp_column(self, column_names):
            """Find the timestamp column in the dataset."""
            possible_ts_columns = ["ts_event", "timestamp", "time", "date"]
            for col in possible_ts_columns:
                if col in column_names:
                    return col
            return None

        def _build_filter_expression(self, ts_column, start_date, end_date, before_datetime):
            """Build PyArrow filter expression for date filtering."""
            filter_parts = []

            if start_date:
                filter_parts.append(ds.field(ts_column) >= pd.Timestamp(start_date, tz="UTC").value)
            if end_date:
                filter_parts.append(ds.field(ts_column) <= pd.Timestamp(end_date, tz="UTC").value)
            if before_datetime:
                filter_parts.append(ds.field(ts_column) < pd.Timestamp(before_datetime, tz="UTC").value)

            if filter_parts:
                if len(filter_parts) == 1:
                    return filter_parts[0]
                else:
                    # Combine with AND
                    result = filter_parts[0]
                    for part in filter_parts[1:]:
                        result = result & part
                    return result
            return None

        def _get_required_columns(self, column_names, ts_column):
            """Get the list of required columns for loading."""
            required = [ts_column, 'open', 'high', 'low', 'close']
            if 'volume' in column_names:
                required.append('volume')
            
            # Only include columns that actually exist
            return [col for col in required if col in column_names]

        def _process_dataframe(self, df, instrument_id):
            """Process and validate the loaded dataframe."""
            if df.empty:
                return df

            # Standardize timestamp
            df = self._standardize_timestamp(df, instrument_id)
            
            # Standardize OHLCV columns
            df = self._standardize_ohlcv_columns(df, instrument_id)
            
            # Validate OHLC data
            df = self._validate_ohlc_data(df)
            
            return df

        def _standardize_timestamp(self, df, instrument_id):
            """Standardize the timestamp column."""
            # Basic implementation - convert to pandas datetime if needed
            if 'timestamp' in df.columns:
                if not pd.api.types.is_datetime64_any_dtype(df['timestamp']):
                    try:
                        df['timestamp'] = pd.to_datetime(df['timestamp'])
                    except Exception as e:
                        self.logger.warning(f"Could not convert timestamp for {instrument_id}: {e}")
            
            return df

        def _standardize_ohlcv_columns(self, df, instrument_id):
            """Standardize OHLCV columns."""
            required_cols = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
            missing_columns = [col for col in required_cols if col not in df.columns]

            if missing_columns:
                self.logger.warning(f"Missing columns for {instrument_id}: {missing_columns}")
                for col in missing_columns:
                    if col == 'volume':
                        df['volume'] = 1.0
                    elif col in ['open', 'high', 'low', 'close']:
                        self.logger.error(f"Critical OHLC column missing: {col}")
                        return pd.DataFrame()

            # Convert to numeric
            for col in ['open', 'high', 'low', 'close', 'volume']:
                if col in df.columns:
                    try:
                        df[col] = pd.to_numeric(df[col], errors='coerce')
                    except Exception as e:
                        self.logger.error(f"Failed to convert {col} to numeric: {e}")
                        df[col] = np.nan

            return df

        def _validate_ohlc_data(self, df):
            """Validate OHLC data relationships and detect statistical anomalies."""
            if df.empty:
                return df

            try:
                # Statistical validation - detect impossible patterns
                if len(df) > 10:
                    up_bars = (df['close'] > df['open']).sum()
                    down_bars = (df['close'] < df['open']).sum()
                    unchanged = (df['close'] == df['open']).sum()
                    total_bars = len(df)
                    
                    up_ratio = up_bars / total_bars
                    
                    # Check for statistically impossible situations
                    if up_ratio > 0.95:
                        self.logger.warning(
                            f"CRITICAL DATA ISSUE: {up_ratio:.1%} of bars are up bars (close > open). "
                            "This is statistically impossible and suggests data corruption."
                        )
                    elif up_ratio < 0.05:
                        self.logger.warning(
                            f"CRITICAL DATA ISSUE: {up_ratio:.1%} of bars are up bars. "
                            "This is statistically unlikely and suggests data corruption."
                        )
                    
                    # Log distribution for debugging
                    self.logger.info(
                        f"Price movement distribution: Up: {up_bars} ({up_ratio:.1%}), "
                        f"Down: {down_bars} ({down_bars/total_bars:.1%}), "
                        f"Unchanged: {unchanged} ({unchanged/total_bars:.1%})"
                    )

                # Fix invalid high/low relationships
                invalid_high = ((df['high'] < df['open']) | (df['high'] < df['close'])).sum()
                invalid_low = ((df['low'] > df['open']) | (df['low'] > df['close'])).sum()

                if invalid_high > 0:
                    self.logger.warning(f"Fixing {invalid_high} bars with invalid high values")
                    mask = (df['high'] < df['open']) | (df['high'] < df['close'])
                    df.loc[mask, 'high'] = df.loc[mask, ['open', 'close']].max(axis=1)

                if invalid_low > 0:
                    self.logger.warning(f"Fixing {invalid_low} bars with invalid low values")
                    mask = (df['low'] > df['open']) | (df['low'] > df['close'])
                    df.loc[mask, 'low'] = df.loc[mask, ['open', 'close']].min(axis=1)

                return df

            except Exception as e:
                self.logger.error(f"Error validating OHLC data: {e}")
                return df

        def _resample_ohlcv(self, df, timeframe):
            """Resample OHLCV data to different timeframes."""
            if df.empty or timeframe == '1min':
                return df

            try:
                # Set timestamp as index
                df_indexed = df.set_index('timestamp')
                
                # Define resampling rules
                timeframe_map = {
                    '5min': '5T',
                    '15min': '15T',
                    '30min': '30T',
                    '1h': '1H',
                    '4h': '4H',
                    '1d': '1D',
                    '1w': '1W'
                }
                
                freq = timeframe_map.get(timeframe, '1T')
                
                # Resample
                resampled = df_indexed.resample(freq).agg({
                    'open': 'first',
                    'high': 'max',
                    'low': 'min',
                    'close': 'last',
                    'volume': 'sum'
                }).dropna()
                
                # Reset index
                resampled.reset_index(inplace=True)
                
                self.logger.info(f"Resampled from {len(df)} to {len(resampled)} bars for {timeframe}")
                return resampled

            except Exception as e:
                self.logger.error(f"Error resampling to {timeframe}: {e}")
                return df

        def _cache_data(self, cache_key, df):
            """Cache the dataframe."""
            self._data_cache[cache_key] = df.copy()
            self._cache_timestamps[cache_key] = time.time()
            
            # Clean up old cache entries if needed
            if len(self._data_cache) > self.config.max_cache_items:
                # Remove oldest entries
                sorted_items = sorted(self._cache_timestamps.items(), key=lambda x: x[1])
                for key, _ in sorted_items[:len(sorted_items)//2]:
                    if key in self._data_cache:
                        del self._data_cache[key]
                    if key in self._cache_timestamps:
                        del self._cache_timestamps[key]


# Global instance for function-based interface compatibility
_global_loader: Optional[ChartDataLoader] = None


def _get_global_loader() -> ChartDataLoader:
    """Get or create the global loader instance."""
    global _global_loader
    if _global_loader is None:
        config = ChartConfig()
        _global_loader = ChartDataLoader(config)
    return _global_loader


def load_instrument_data(
    instrument_id: str,
    timeframe: str = '1min',
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    limit: Optional[int] = None,
    before_datetime: Optional[datetime] = None,
    use_cache: bool = True
):
    """Function-based interface for loading instrument data."""
    loader = _get_global_loader()
    return loader.load_instrument_data(
        instrument_id, timeframe, start_date, end_date, limit, before_datetime, use_cache
    )


def load_instrument_data_optimized(
    instrument_id: str,
    timeframe: str = '1min',
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    limit: Optional[int] = None,
    before_datetime: Optional[datetime] = None,
    use_cache: bool = True
):
    """Function-based interface for optimized loading that reuses cached 1min data."""
    loader = _get_global_loader()
    return loader.load_instrument_data_optimized(
        instrument_id, timeframe, start_date, end_date, limit, before_datetime, use_cache
    )


def get_available_instruments():
    """Function-based interface for getting available instruments."""
    loader = _get_global_loader()
    return []  # Placeholder - implement if needed


def set_catalog_path(path: str):
    """Function-based interface for setting catalog path."""
    global _global_loader
    config = ChartConfig(catalog_path=path)
    _global_loader = ChartDataLoader(config)


def clear_arrow_cache(instrument_id: Optional[str] = None):
    """Function-based interface for clearing cache."""
    loader = _get_global_loader()
    # Placeholder - implement if needed
    pass
