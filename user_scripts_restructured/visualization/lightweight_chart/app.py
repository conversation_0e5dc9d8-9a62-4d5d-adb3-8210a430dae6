"""
Flask application for TradingView Lightweight Charts visualization.

This module provides the main Flask application for serving interactive
financial charts using TradingView's Lightweight Charts library.
"""

import os
import time
import logging
import json
from datetime import datetime
from typing import Optional, Dict, Any

try:
    from flask import Flask, render_template, jsonify, request, redirect, url_for
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False
    Flask = None

try:
    import numpy as np
    import pandas as pd
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False
    np = None
    pd = None

try:
    from .config import ChartConfig, ChartConfigManager
    from .utils.data_loader import load_instrument_data, load_instrument_data_optimized, get_available_instruments, set_catalog_path, clear_arrow_cache
    from .utils.chart_utils import create_chart_response
    from .utils.test_data import create_test_catalog
except ImportError:
    # For standalone testing
    try:
        from config import ChartConfig, ChartConfigManager
        from utils.data_loader import load_instrument_data, load_instrument_data_optimized, get_available_instruments, set_catalog_path, clear_arrow_cache
        from utils.chart_utils import create_chart_response
        from utils.test_data import create_test_catalog
    except ImportError:
        # Fallback for missing utils
        def create_chart_response(*args, **kwargs):
            return {'error': 'Chart utils not available'}
        def create_test_catalog(*args, **kwargs):
            return None

        # Create minimal config classes if not available
        class ChartConfig:
            def __init__(self, **kwargs):
                self.host = kwargs.get('host', '0.0.0.0')
                self.port = kwargs.get('port', 8080)
                self.catalog_path = kwargs.get('catalog_path', '/tmp/test_catalog')
                self.debug = kwargs.get('debug', False)
                self.max_points = kwargs.get('max_points', 1000)
                self.enable_test_data = kwargs.get('enable_test_data', False)
                self.test_symbols = kwargs.get('test_symbols', 'BTCUSDT')
                self.test_bars = kwargs.get('test_bars', 1000)

            def validate(self):
                return True

            def get_server_url(self):
                return f"http://{self.host}:{self.port}"

        class ChartConfigManager:
            def __init__(self):
                pass

logger = logging.getLogger(__name__)


class NumpyEncoder(json.JSONEncoder):
    """Custom JSON encoder to handle numpy and pandas types."""
    def default(self, obj):
        if NUMPY_AVAILABLE:
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, (pd.Timestamp, pd.DatetimeIndex)):
                return obj.isoformat() if hasattr(obj, 'isoformat') else str(obj)
        return super().default(obj)


def safe_jsonify(data, status_code=200):
    """Custom jsonify that handles numpy types."""
    try:
        json_str = json.dumps(data, cls=NumpyEncoder)
        response = Flask.response_class(
            json_str,
            mimetype='application/json'
        )
        response.status_code = status_code
        return response
    except Exception as e:
        logger.error(f"Error in safe_jsonify: {e}")
        # Fallback to regular jsonify with error
        return jsonify({'error': 'JSON serialization error'}), 500


class ChartServer:
    """
    TradingView Lightweight Charts server implementation.

    This class provides a Flask-based web server for visualizing financial data
    using TradingView's Lightweight Charts library.
    """

    def __init__(self, config: ChartConfig):
        """
        Initialize the chart server.

        Args:
            config: Chart configuration
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.app = None

        if not FLASK_AVAILABLE:
            raise ImportError("Flask is required for ChartServer. Install with: pip install Flask")

        # Set the catalog path using the functional approach
        set_catalog_path(config.catalog_path)

        self._setup_flask_app()

    def _setup_flask_app(self):
        """Set up the Flask application with routes."""
        self.app = Flask(
            __name__,
            template_folder='templates',
            static_folder='static'
        )

        # Configure Flask
        self.app.config['SECRET_KEY'] = 'nautilus-chart-server'
        
        # Set custom JSON encoder to handle numpy types
        if hasattr(self.app, 'json_encoder'):
            self.app.json_encoder = NumpyEncoder
        elif hasattr(self.app, 'json'):
            self.app.json.encoder = NumpyEncoder

        # Register routes
        self._register_routes()

        # Set up logging
        if not self.config.debug:
            log = logging.getLogger('werkzeug')
            log.setLevel(logging.WARNING)

    def _register_routes(self):
        """Register Flask routes."""

        @self.app.route('/')
        def index():
            """Render the index page with available instruments."""
            try:
                instruments = get_available_instruments()
                return render_template('index.html', instruments=instruments)
            except Exception as e:
                logger.error(f"Error loading index page: {e}")
                return render_template('index.html', instruments=[])

        @self.app.route('/chart/<instrument_id>')
        def chart(instrument_id: str):
            """Render the chart page for a specific instrument."""
            return render_template('chart.html', instrument_id=instrument_id)

        @self.app.route('/api/instruments')
        def get_instruments():
            """Return available instruments as JSON."""
            try:
                instruments = get_available_instruments()
                return safe_jsonify(instruments)
            except Exception as e:
                logger.error(f"Error getting instruments: {e}")
                return safe_jsonify({'error': str(e)}, 500)

        @self.app.route('/api/chart-data/<instrument_id>')
        def get_chart_data(instrument_id: str):
            """Return chart data for a specific instrument."""
            try:
                # Parse request parameters
                timeframe = request.args.get('timeframe', '1min')
                before_timestamp_seconds_str = request.args.get('before_timestamp_seconds')
                limit_str = request.args.get('limit', str(self.config.max_points))

                # Parse parameters
                before_datetime = None
                if before_timestamp_seconds_str:
                    try:
                        before_datetime = datetime.fromtimestamp(int(before_timestamp_seconds_str))
                    except ValueError:
                        logger.warning(f"Invalid before_timestamp_seconds: {before_timestamp_seconds_str}")
                        return jsonify({'error': 'Invalid before_timestamp_seconds format'}), 400

                try:
                    limit = int(limit_str)
                    if limit <= 0:
                        limit = self.config.max_points
                except ValueError:
                    logger.warning(f"Invalid limit: {limit_str}")
                    return jsonify({'error': 'Invalid limit format'}), 400

                # Load data using optimized loader for better performance
                df = load_instrument_data_optimized(
                    instrument_id=instrument_id,
                    timeframe=timeframe,
                    limit=limit,
                    before_datetime=before_datetime
                )

                if df.empty:
                    message = ('No more historical data found' if before_datetime
                             else f'No data found for instrument {instrument_id} with timeframe {timeframe}')
                    status_code = 200 if before_datetime else 404

                    return safe_jsonify({
                        'ohlc': [],
                        'volume': [],
                        'instrument': instrument_id,
                        'timeframe': timeframe,
                        'message': message
                    }, status_code)

                # Create chart response
                response = create_chart_response(
                    df=df,
                    instrument_id=instrument_id,
                    timeframe=timeframe,
                    include_moving_averages=False
                )

                return safe_jsonify(response)

            except Exception as e:
                logger.error(f"Error getting chart data for {instrument_id}: {e}")
                return safe_jsonify({'error': str(e)}, 500)

        @self.app.route('/api/health')
        def health_check():
            """Health check endpoint."""
            try:
                health_status = self._health_check()
                return jsonify(health_status)
            except Exception as e:
                logger.error(f"Health check failed: {e}")
                return jsonify({'status': 'error', 'error': str(e)}), 500

        @self.app.route('/api/cache/clear')
        def clear_cache():
            """Clear data cache."""
            try:
                instrument_id = request.args.get('instrument_id')
                clear_arrow_cache(instrument_id)

                message = (f"Cache cleared for {instrument_id}" if instrument_id
                          else "All caches cleared")

                return jsonify({'message': message})
            except Exception as e:
                logger.error(f"Error clearing cache: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/cache/stats')
        def cache_stats():
            """Get cache statistics."""
            try:
                # Import the cache dictionaries to get stats
                from .utils.data_loader import DATA_CACHE, ARROW_DATASET_CACHE, CACHE_TIMESTAMPS
                stats = {
                    'data_cache_size': len(DATA_CACHE),
                    'arrow_cache_size': len(ARROW_DATASET_CACHE),
                    'cache_timestamps_size': len(CACHE_TIMESTAMPS)
                }
                return jsonify(stats)
            except Exception as e:
                logger.error(f"Error getting cache stats: {e}")
                return jsonify({'error': str(e)}), 500

    def start_server(self) -> None:
        """Start the Flask server."""
        try:
            # Validate configuration
            if not self.config.validate():
                raise ValueError("Invalid configuration")

            # Create test data if requested
            if self.config.enable_test_data:
                self._create_test_data()

            # Log startup information
            logger.info(f"Starting Nautilus Trader Chart Server")
            logger.info(f"Server URL: {self.config.get_server_url()}")
            logger.info(f"Catalog path: {self.config.catalog_path}")
            logger.info(f"Debug mode: {self.config.debug}")

            # Start Flask server
            self.app.run(
                host=self.config.host,
                port=self.config.port,
                debug=self.config.debug,
                threaded=True
            )

        except Exception as e:
            logger.error(f"Error starting server: {e}")
            raise

    def run(self):
        """Run the Flask server."""
        try:
            self.app.run(
                host=self.config.host,
                port=self.config.port,
                debug=self.config.debug,
                threaded=True
            )
        except Exception as e:
            self.logger.error(f"Failed to start server: {e}")
            raise

    def stop_server(self) -> None:
        """Stop the Flask server."""
        # Flask doesn't have a built-in way to stop the server programmatically
        # This would typically be handled by the WSGI server in production
        logger.info("Server stop requested")

    def get_available_instruments(self) -> list:
        """Get available instruments from the data loader."""
        return get_available_instruments()

    def validate_data_source(self, path: str) -> bool:
        """Validate that the data source is accessible."""
        try:
            if not os.path.exists(path):
                return False

            # Check if it's a directory with the expected structure
            data_dir = os.path.join(path, "data", "bar")
            if not os.path.exists(data_dir):
                return False

            # Check if there are any instrument directories
            try:
                subdirs = [d for d in os.listdir(data_dir)
                          if os.path.isdir(os.path.join(data_dir, d))]
                return len(subdirs) > 0
            except OSError:
                return False

        except Exception as e:
            logger.error(f"Error validating data source {path}: {e}")
            return False

    def _create_test_data(self):
        """Create test data if it doesn't exist."""
        try:
            if not os.path.exists(self.config.catalog_path):
                logger.info("Creating test catalog with sample data")

                symbols = self.config.test_symbols.split(',')
                success = create_test_catalog(
                    catalog_path=self.config.catalog_path,
                    symbols=symbols,
                    bars_per_symbol=self.config.test_bars
                )

                if success:
                    logger.info(f"Test catalog created at {self.config.catalog_path}")
                else:
                    logger.error("Failed to create test catalog")
            else:
                logger.info(f"Using existing catalog at {self.config.catalog_path}")

        except Exception as e:
            logger.error(f"Error creating test data: {e}")

    def _health_check(self) -> dict:
        """Perform health check."""
        try:
            # Check if catalog path exists and is accessible
            catalog_accessible = self.validate_data_source(self.config.catalog_path)
            
            # Get available instruments count
            instruments = get_available_instruments()
            instrument_count = len(instruments)
            
            return {
                'status': 'healthy' if catalog_accessible else 'unhealthy',
                'catalog_path': self.config.catalog_path,
                'catalog_accessible': catalog_accessible,
                'instrument_count': instrument_count,
                'server_url': self.config.get_server_url()
            }
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }


def create_app(config=None):
    """
    Create and configure Flask application.

    Args:
        config: Chart configuration (optional)

    Returns:
        Configured Flask application
    """
    if config is None:
        config = ChartConfig()

    server = ChartServer(config)
    return server.app


def run_server(
    host: str = "0.0.0.0",
    port: int = 8080,
    catalog_path: str = "/tmp/test_catalog",
    debug: bool = False,
    log_level: str = "INFO",
    **kwargs
) -> None:
    """
    Run the chart server with specified configuration.

    Args:
        host: Host to bind to
        port: Port to listen on
        catalog_path: Path to the data catalog
        debug: Enable debug mode
        log_level: Logging level
        **kwargs: Additional configuration options
    """
    try:
        # Create configuration
        config_dict = {
            'host': host,
            'port': port,
            'catalog_path': catalog_path,
            'debug': debug,
            'log_level': log_level,
            **kwargs
        }

        config = ChartConfig(**config_dict)

        # Create and start server
        server = ChartServer(config)
        server.start_server()

    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {e}")
        raise


if __name__ == "__main__":
    # Basic command line interface
    import argparse

    parser = argparse.ArgumentParser(description="Nautilus Trader Chart Server")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8080, help="Port to listen on")
    parser.add_argument("--catalog", default="/tmp/test_catalog", help="Catalog path")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    parser.add_argument("--log-level", default="INFO", help="Logging level")
    parser.add_argument("--test-data", action="store_true", help="Create test data")

    args = parser.parse_args()

    run_server(
        host=args.host,
        port=args.port,
        catalog_path=args.catalog,
        debug=args.debug,
        log_level=args.log_level,
        enable_test_data=args.test_data
    )
