#!/usr/bin/env python3
"""
Test script for the refactored lightweight chart visualization.

This script tests the basic functionality of the refactored chart server
to ensure it works correctly after consolidation and bug fixes.
"""

import os
import sys
import logging
import tempfile
from pathlib import Path

# Add the current directory to Python path for imports
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def setup_logging():
    """Set up logging for the test."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def test_imports():
    """Test that all modules can be imported successfully."""
    print("Testing imports...")
    
    try:
        from config import ChartConfig, ChartConfigManager
        print("✓ Config module imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import config: {e}")
        return False
    
    try:
        from utils.data_loader import load_instrument_data, get_available_instruments
        print("✓ Data loader module imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import data loader: {e}")
        return False
    
    try:
        from utils.chart_utils import create_chart_response, format_chart_data
        print("✓ Chart utils module imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import chart utils: {e}")
        return False
    
    try:
        from utils.test_data import create_test_catalog
        print("✓ Test data module imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import test data: {e}")
        return False
    
    try:
        from app import ChartServer, create_app
        print("✓ App module imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import app: {e}")
        return False
    
    return True

def test_config():
    """Test configuration functionality."""
    print("\nTesting configuration...")
    
    try:
        from config import ChartConfig
        
        # Test default config
        config = ChartConfig()
        assert config.host == "0.0.0.0"
        assert config.port == 8080
        assert config.validate()
        print("✓ Default configuration works")
        
        # Test custom config
        config = ChartConfig(
            host="127.0.0.1",
            port=9090,
            catalog_path="/tmp/test_catalog",
            debug=True
        )
        assert config.host == "127.0.0.1"
        assert config.port == 9090
        assert config.validate()
        print("✓ Custom configuration works")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False

def test_data_loader():
    """Test data loader functionality."""
    print("\nTesting data loader...")
    
    try:
        from utils.data_loader import set_catalog_path, get_available_instruments
        
        # Create a temporary catalog path
        with tempfile.TemporaryDirectory() as temp_dir:
            set_catalog_path(temp_dir)
            
            # Test getting instruments (should be empty)
            instruments = get_available_instruments()
            assert isinstance(instruments, list)
            print("✓ Data loader basic functionality works")
        
        return True
        
    except Exception as e:
        print(f"✗ Data loader test failed: {e}")
        return False

def test_test_data():
    """Test test data generation."""
    print("\nTesting test data generation...")
    
    try:
        from utils.test_data import create_test_catalog
        
        # Create test data in temporary directory
        with tempfile.TemporaryDirectory() as temp_dir:
            success = create_test_catalog(
                catalog_path=temp_dir,
                symbols=["TEST"],
                bars_per_symbol=100
            )
            
            assert success
            
            # Check that files were created
            data_dir = Path(temp_dir) / "data" / "bar"
            assert data_dir.exists()
            
            # Check for instrument directory
            instrument_dirs = list(data_dir.iterdir())
            assert len(instrument_dirs) > 0
            print("✓ Test data generation works")
        
        return True
        
    except Exception as e:
        print(f"✗ Test data generation failed: {e}")
        return False

def test_chart_utils():
    """Test chart utilities."""
    print("\nTesting chart utilities...")
    
    try:
        import pandas as pd
        import numpy as np
        from datetime import datetime, timedelta
        from utils.chart_utils import format_chart_data, create_chart_response
        
        # Create sample data
        dates = pd.date_range(start=datetime.now() - timedelta(days=1), periods=10, freq='1min')
        data = {
            'timestamp': dates,
            'open': np.random.uniform(100, 110, 10),
            'high': np.random.uniform(110, 120, 10),
            'low': np.random.uniform(90, 100, 10),
            'close': np.random.uniform(100, 110, 10),
            'volume': np.random.randint(1000, 10000, 10)
        }
        df = pd.DataFrame(data)
        
        # Test format_chart_data
        ohlc_data, volume_data = format_chart_data(df)
        assert len(ohlc_data) == 10
        assert len(volume_data) == 10
        print("✓ Chart data formatting works")
        
        # Test create_chart_response
        response = create_chart_response(df, "TEST", "1min")
        assert 'ohlc' in response
        assert 'volume' in response
        assert response['instrument'] == "TEST"
        print("✓ Chart response creation works")
        
        return True
        
    except Exception as e:
        print(f"✗ Chart utils test failed: {e}")
        return False

def test_app_creation():
    """Test Flask app creation."""
    print("\nTesting Flask app creation...")
    
    try:
        from config import ChartConfig
        from app import create_app
        
        # Create config
        config = ChartConfig(
            catalog_path="/tmp/test_catalog",
            debug=True
        )
        
        # Create app
        app = create_app(config)
        assert app is not None
        print("✓ Flask app creation works")
        
        return True
        
    except Exception as e:
        print(f"✗ Flask app creation failed: {e}")
        return False

def main():
    """Run all tests."""
    setup_logging()
    
    print("=" * 60)
    print("Testing Refactored Lightweight Chart Visualization")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_config,
        test_data_loader,
        test_test_data,
        test_chart_utils,
        test_app_creation
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed} passed, {failed} failed")
    print("=" * 60)
    
    if failed == 0:
        print("🎉 All tests passed! The refactored version is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
